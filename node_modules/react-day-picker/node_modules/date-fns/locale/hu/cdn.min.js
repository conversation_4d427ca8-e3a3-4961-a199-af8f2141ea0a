(()=>{var K;function I(B){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},I(B)}function N(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),H.push.apply(H,J)}return H}function A(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?N(Object(H),!0).forEach(function(J){D(B,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):N(Object(H)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(H,J))})}return B}function D(B,G,H){if(G=W(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function W(B){var G=V(B,"string");return I(G)=="symbol"?G:String(G)}function V(B,G){if(I(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var J=H.call(B,G||"default");if(I(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var x=Object.defineProperty,ZB=function B(G,H){for(var J in H)x(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},M={about:"k\xF6r\xFClbel\xFCl",over:"t\xF6bb mint",almost:"majdnem",lessthan:"kevesebb mint"},R={xseconds:" m\xE1sodperc",halfaminute:"f\xE9l perc",xminutes:" perc",xhours:" \xF3ra",xdays:" nap",xweeks:" h\xE9t",xmonths:" h\xF3nap",xyears:" \xE9v"},L={xseconds:{"-1":" m\xE1sodperccel ezel\u0151tt",1:" m\xE1sodperc m\xFAlva",0:" m\xE1sodperce"},halfaminute:{"-1":"f\xE9l perccel ezel\u0151tt",1:"f\xE9l perc m\xFAlva",0:"f\xE9l perce"},xminutes:{"-1":" perccel ezel\u0151tt",1:" perc m\xFAlva",0:" perce"},xhours:{"-1":" \xF3r\xE1val ezel\u0151tt",1:" \xF3ra m\xFAlva",0:" \xF3r\xE1ja"},xdays:{"-1":" nappal ezel\u0151tt",1:" nap m\xFAlva",0:" napja"},xweeks:{"-1":" h\xE9ttel ezel\u0151tt",1:" h\xE9t m\xFAlva",0:" hete"},xmonths:{"-1":" h\xF3nappal ezel\u0151tt",1:" h\xF3nap m\xFAlva",0:" h\xF3napja"},xyears:{"-1":" \xE9vvel ezel\u0151tt",1:" \xE9v m\xFAlva",0:" \xE9ve"}},S=function B(G,H,J){var X=G.match(/about|over|almost|lessthan/i),Y=X?G.replace(X[0],""):G,Z=(J===null||J===void 0?void 0:J.addSuffix)===!0,T=Y.toLowerCase(),C=(J===null||J===void 0?void 0:J.comparison)||0,U=Z?L[T][C]:R[T],Q=T==="halfaminute"?U:H+U;if(X){var $=X[0].toLowerCase();Q=M[$]+" "+Q}return Q};function E(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,J=B.formats[H]||B.formats[B.defaultWidth];return J}}var j={full:"y. MMMM d., EEEE",long:"y. MMMM d.",medium:"y. MMM d.",short:"y. MM. dd."},_={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},w={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},P={date:E({formats:j,defaultWidth:"full"}),time:E({formats:_,defaultWidth:"full"}),dateTime:E({formats:w,defaultWidth:"full"})};function z(B){return function(G){var H=F[G.getDay()],J=B?"":"'m\xFAlt' ";return"".concat(J,"'").concat(H,"' p'-kor'")}}var F=["vas\xE1rnap","h\xE9tf\u0151n","kedden","szerd\xE1n","cs\xFCt\xF6rt\xF6k\xF6n","p\xE9nteken","szombaton"],f={lastWeek:z(!1),yesterday:"'tegnap' p'-kor'",today:"'ma' p'-kor'",tomorrow:"'holnap' p'-kor'",nextWeek:z(!0),other:"P"},v=function B(G,H){var J=f[G];if(typeof J==="function")return J(H);return J};function O(B){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=B.formattingValues[Z]||B.formattingValues[Y]}else{var T=B.defaultWidth,C=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;X=B.values[C]||B.values[T]}var U=B.argumentCallback?B.argumentCallback(G):G;return X[U]}}var k={narrow:["ie.","isz."],abbreviated:["i. e.","i. sz."],wide:["Krisztus el\u0151tt","id\u0151sz\xE1m\xEDt\xE1sunk szerint"]},b={narrow:["1.","2.","3.","4."],abbreviated:["1. n.\xE9v","2. n.\xE9v","3. n.\xE9v","4. n.\xE9v"],wide:["1. negyed\xE9v","2. negyed\xE9v","3. negyed\xE9v","4. negyed\xE9v"]},h={narrow:["I.","II.","III.","IV."],abbreviated:["I. n.\xE9v","II. n.\xE9v","III. n.\xE9v","IV. n.\xE9v"],wide:["I. negyed\xE9v","II. negyed\xE9v","III. negyed\xE9v","IV. negyed\xE9v"]},m={narrow:["J","F","M","\xC1","M","J","J","A","Sz","O","N","D"],abbreviated:["jan.","febr.","m\xE1rc.","\xE1pr.","m\xE1j.","j\xFAn.","j\xFAl.","aug.","szept.","okt.","nov.","dec."],wide:["janu\xE1r","febru\xE1r","m\xE1rcius","\xE1prilis","m\xE1jus","j\xFAnius","j\xFAlius","augusztus","szeptember","okt\xF3ber","november","december"]},c={narrow:["V","H","K","Sz","Cs","P","Sz"],short:["V","H","K","Sze","Cs","P","Szo"],abbreviated:["V","H","K","Sze","Cs","P","Szo"],wide:["vas\xE1rnap","h\xE9tf\u0151","kedd","szerda","cs\xFCt\xF6rt\xF6k","p\xE9ntek","szombat"]},y={narrow:{am:"de.",pm:"du.",midnight:"\xE9jf\xE9l",noon:"d\xE9l",morning:"reggel",afternoon:"du.",evening:"este",night:"\xE9jjel"},abbreviated:{am:"de.",pm:"du.",midnight:"\xE9jf\xE9l",noon:"d\xE9l",morning:"reggel",afternoon:"du.",evening:"este",night:"\xE9jjel"},wide:{am:"de.",pm:"du.",midnight:"\xE9jf\xE9l",noon:"d\xE9l",morning:"reggel",afternoon:"d\xE9lut\xE1n",evening:"este",night:"\xE9jjel"}},g=function B(G,H){var J=Number(G);return J+"."},p={ordinalNumber:g,era:O({values:k,defaultWidth:"wide"}),quarter:O({values:b,defaultWidth:"wide",argumentCallback:function B(G){return G-1},formattingValues:h,defaultFormattingWidth:"wide"}),month:O({values:m,defaultWidth:"wide"}),day:O({values:c,defaultWidth:"wide"}),dayPeriod:O({values:y,defaultWidth:"wide"})};function q(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],T=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],C=Array.isArray(T)?l(T,function($){return $.test(Z)}):u(T,function($){return $.test(Z)}),U;U=B.valueCallback?B.valueCallback(C):C,U=H.valueCallback?H.valueCallback(U):U;var Q=G.slice(Z.length);return{value:U,rest:Q}}}function u(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function l(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function d(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(B.matchPattern);if(!J)return null;var X=J[0],Y=G.match(B.parsePattern);if(!Y)return null;var Z=B.valueCallback?B.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var T=G.slice(X.length);return{value:Z,rest:T}}}var i=/^(\d+)\.?/i,n=/\d+/i,s={narrow:/^(ie\.|isz\.)/i,abbreviated:/^(i\.\s?e\.?|b?\s?c\s?e|i\.\s?sz\.?)/i,wide:/^(Krisztus előtt|időszámításunk előtt|időszámításunk szerint|i\. sz\.)/i},o={narrow:[/ie/i,/isz/i],abbreviated:[/^(i\.?\s?e\.?|b\s?ce)/i,/^(i\.?\s?sz\.?|c\s?e)/i],any:[/előtt/i,/(szerint|i. sz.)/i]},r={narrow:/^[1234]\.?/i,abbreviated:/^[1234]?\.?\s?n\.év/i,wide:/^([1234]|I|II|III|IV)?\.?\s?negyedév/i},a={any:[/1|I$/i,/2|II$/i,/3|III/i,/4|IV/i]},e={narrow:/^[jfmaásond]|sz/i,abbreviated:/^(jan\.?|febr\.?|márc\.?|ápr\.?|máj\.?|jún\.?|júl\.?|aug\.?|szept\.?|okt\.?|nov\.?|dec\.?)/i,wide:/^(január|február|március|április|május|június|július|augusztus|szeptember|október|november|december)/i},t={narrow:[/^j/i,/^f/i,/^m/i,/^a|á/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s|sz/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^már/i,/^áp/i,/^máj/i,/^jún/i,/^júl/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},BB={narrow:/^([vhkpc]|sz|cs|sz)/i,short:/^([vhkp]|sze|cs|szo)/i,abbreviated:/^([vhkp]|sze|cs|szo)/i,wide:/^(vasárnap|hétfő|kedd|szerda|csütörtök|péntek|szombat)/i},GB={narrow:[/^v/i,/^h/i,/^k/i,/^sz/i,/^c/i,/^p/i,/^sz/i],any:[/^v/i,/^h/i,/^k/i,/^sze/i,/^c/i,/^p/i,/^szo/i]},HB={any:/^((de|du)\.?|éjfél|délután|dél|reggel|este|éjjel)/i},JB={any:{am:/^de\.?/i,pm:/^du\.?/i,midnight:/^éjf/i,noon:/^dé/i,morning:/reg/i,afternoon:/^délu\.?/i,evening:/es/i,night:/éjj/i}},XB={ordinalNumber:d({matchPattern:i,parsePattern:n,valueCallback:function B(G){return parseInt(G,10)}}),era:q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),quarter:q({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:q({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"}),day:q({matchPatterns:BB,defaultMatchWidth:"wide",parsePatterns:GB,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:HB,defaultMatchWidth:"any",parsePatterns:JB,defaultParseWidth:"any"})},YB={code:"hu",formatDistance:S,formatLong:P,formatRelative:v,localize:p,match:XB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=A(A({},window.dateFns),{},{locale:A(A({},(K=window.dateFns)===null||K===void 0?void 0:K.locale),{},{hu:YB})})})();

//# debugId=4DED7B38CED9FB1464756E2164756E21
