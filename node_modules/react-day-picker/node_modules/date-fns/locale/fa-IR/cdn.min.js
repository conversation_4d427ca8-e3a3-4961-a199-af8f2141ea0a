(()=>{var I;function C(G){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},C(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){N(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function N(G,H,J){if(H=z(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function z(G){var H=E(G,"string");return C(H)=="symbol"?H:String(H)}function E(G,H){if(C(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(C(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062B\u0627\u0646\u06CC\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062B\u0627\u0646\u06CC\u0647"},xSeconds:{one:"1 \u062B\u0627\u0646\u06CC\u0647",other:"{{count}} \u062B\u0627\u0646\u06CC\u0647"},halfAMinute:"\u0646\u06CC\u0645 \u062F\u0642\u06CC\u0642\u0647",lessThanXMinutes:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062F\u0642\u06CC\u0642\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062F\u0642\u06CC\u0642\u0647"},xMinutes:{one:"1 \u062F\u0642\u06CC\u0642\u0647",other:"{{count}} \u062F\u0642\u06CC\u0642\u0647"},aboutXHours:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0639\u062A",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0639\u062A"},xHours:{one:"1 \u0633\u0627\u0639\u062A",other:"{{count}} \u0633\u0627\u0639\u062A"},xDays:{one:"1 \u0631\u0648\u0632",other:"{{count}} \u0631\u0648\u0632"},aboutXWeeks:{one:"\u062D\u062F\u0648\u062F 1 \u0647\u0641\u062A\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0647\u0641\u062A\u0647"},xWeeks:{one:"1 \u0647\u0641\u062A\u0647",other:"{{count}} \u0647\u0641\u062A\u0647"},aboutXMonths:{one:"\u062D\u062F\u0648\u062F 1 \u0645\u0627\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0645\u0627\u0647"},xMonths:{one:"1 \u0645\u0627\u0647",other:"{{count}} \u0645\u0627\u0647"},aboutXYears:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0644",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0644"},xYears:{one:"1 \u0633\u0627\u0644",other:"{{count}} \u0633\u0627\u0644"},overXYears:{one:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 1 \u0633\u0627\u0644",other:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 {{count}} \u0633\u0627\u0644"},almostXYears:{one:"\u0646\u0632\u062F\u06CC\u06A9 1 \u0633\u0627\u0644",other:"\u0646\u0632\u062F\u06CC\u06A9 {{count}} \u0633\u0627\u0644"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\u062F\u0631 "+Y;else return Y+" \u0642\u0628\u0644";return Y};function $(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"yyyy/MM/dd"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} '\u062F\u0631' {{time}}",long:"{{date}} '\u062F\u0631' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee '\u06AF\u0630\u0634\u062A\u0647 \u062F\u0631' p",yesterday:"'\u062F\u06CC\u0631\u0648\u0632 \u062F\u0631' p",today:"'\u0627\u0645\u0631\u0648\u0632 \u062F\u0631' p",tomorrow:"'\u0641\u0631\u062F\u0627 \u062F\u0631' p",nextWeek:"eeee '\u062F\u0631' p",other:"P"},w=function G(H,J,X,Y){return j[H]};function O(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[B]||G.formattingValues[Z]}else{var T=G.defaultWidth,A=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[A]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645.","\u0628.\u0645."],wide:["\u0642\u0628\u0644 \u0627\u0632 \u0645\u06CC\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0632 \u0645\u06CC\u0644\u0627\u062F"]},f={narrow:["1","2","3","4"],abbreviated:["\u0633\u200C\u06451","\u0633\u200C\u06452","\u0633\u200C\u06453","\u0633\u200C\u06454"],wide:["\u0633\u0647\u200C\u0645\u0627\u0647\u0647 1","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 2","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 3","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 4"]},v={narrow:["\u0698","\u0641","\u0645","\u0622","\u0645","\u062C","\u062C","\u0622","\u0633","\u0627","\u0646","\u062F"],abbreviated:["\u0698\u0627\u0646\u0640","\u0641\u0648\u0631","\u0645\u0627\u0631\u0633","\u0622\u067E\u0631","\u0645\u06CC","\u062C\u0648\u0646","\u062C\u0648\u0644\u0640","\u0622\u06AF\u0648","\u0633\u067E\u062A\u0640","\u0627\u06A9\u062A\u0640","\u0646\u0648\u0627\u0645\u0640","\u062F\u0633\u0627\u0645\u0640"],wide:["\u0698\u0627\u0646\u0648\u06CC\u0647","\u0641\u0648\u0631\u06CC\u0647","\u0645\u0627\u0631\u0633","\u0622\u067E\u0631\u06CC\u0644","\u0645\u06CC","\u062C\u0648\u0646","\u062C\u0648\u0644\u0627\u06CC","\u0622\u06AF\u0648\u0633\u062A","\u0633\u067E\u062A\u0627\u0645\u0628\u0631","\u0627\u06A9\u062A\u0628\u0631","\u0646\u0648\u0627\u0645\u0628\u0631","\u062F\u0633\u0627\u0645\u0628\u0631"]},F={narrow:["\u06CC","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],short:["1\u0634","2\u0634","3\u0634","4\u0634","5\u0634","\u062C","\u0634"],abbreviated:["\u06CC\u06A9\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"],wide:["\u06CC\u06A9\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"]},P={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},k={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},b=function G(H,J){return String(H)},h={ordinalNumber:b,era:O({values:_,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:O({values:v,defaultWidth:"wide"}),day:O({values:F,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function Q(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var B=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],A=Array.isArray(T)?c(T,function(K){return K.test(B)}):m(T,function(K){return K.test(B)}),U;U=G.valueCallback?G.valueCallback(A):A,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(B.length);return{value:U,rest:HG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function y(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var B=G.valueCallback?G.valueCallback(Z[0]):Z[0];B=J.valueCallback?J.valueCallback(B):B;var T=H.slice(Y.length);return{value:B,rest:T}}}var p=/^(\d+)(th|st|nd|rd)?/i,d=/\d+/i,g={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?م\.?|ق\.?\s?د\.?\s?م\.?|م\.?\s?|د\.?\s?م\.?)/i,wide:/^(قبل از میلاد|قبل از دوران مشترک|میلادی|دوران مشترک|بعد از میلاد)/i},u={any:[/^قبل/i,/^بعد/i]},l={narrow:/^[1234]/i,abbreviated:/^س‌م[1234]/i,wide:/^سه‌ماهه [1234]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[جژفمآاماسند]/i,abbreviated:/^(جنو|ژانـ|ژانویه|فوریه|فور|مارس|آوریل|آپر|مه|می|ژوئن|جون|جول|جولـ|ژوئیه|اوت|آگو|سپتمبر|سپتامبر|اکتبر|اکتوبر|نوامبر|نوامـ|دسامبر|دسامـ|دسم)/i,wide:/^(ژانویه|جنوری|فبروری|فوریه|مارچ|مارس|آپریل|اپریل|ایپریل|آوریل|مه|می|ژوئن|جون|جولای|ژوئیه|آگست|اگست|آگوست|اوت|سپتمبر|سپتامبر|اکتبر|اکتوبر|نوامبر|نومبر|دسامبر|دسمبر)/i},s={narrow:[/^(ژ|ج)/i,/^ف/i,/^م/i,/^(آ|ا)/i,/^م/i,/^(ژ|ج)/i,/^(ج|ژ)/i,/^(آ|ا)/i,/^س/i,/^ا/i,/^ن/i,/^د/i],any:[/^ژا/i,/^ف/i,/^ما/i,/^آپ/i,/^(می|مه)/i,/^(ژوئن|جون)/i,/^(ژوئی|جول)/i,/^(اوت|آگ)/i,/^س/i,/^(اوک|اک)/i,/^ن/i,/^د/i]},o={narrow:/^[شیدسچپج]/i,short:/^(ش|ج|1ش|2ش|3ش|4ش|5ش)/i,abbreviated:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i,wide:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i},r={narrow:[/^ی/i,/^دو/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i],any:[/^(ی|1ش|یکشنبه)/i,/^(د|2ش|دوشنبه)/i,/^(س|3ش|سه‌شنبه)/i,/^(چ|4ش|چهارشنبه)/i,/^(پ|5ش|پنجشنبه)/i,/^(ج|جمعه)/i,/^(ش|شنبه)/i]},e={narrow:/^(ب|ق|ن|ظ|ص|ب.ظ.|ع|ش)/i,abbreviated:/^(ق.ظ.|ب.ظ.|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i,wide:/^(قبل‌ازظهر|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i},a={any:{am:/^(ق|ق.ظ.|قبل‌ازظهر)/i,pm:/^(ب|ب.ظ.|بعدازظهر)/i,midnight:/^(‌نیمه‌شب|ن)/i,noon:/^(ظ|ظهر)/i,morning:/(ص|صبح)/i,afternoon:/(ب|ب.ظ.|بعدازظهر)/i,evening:/(ع|عصر)/i,night:/(ش|شب)/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"})},GG={code:"fa-IR",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:6,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(I=window.dateFns)===null||I===void 0?void 0:I.locale),{},{faIR:GG})})})();

//# debugId=81497A524471CAEE64756E2164756E21
