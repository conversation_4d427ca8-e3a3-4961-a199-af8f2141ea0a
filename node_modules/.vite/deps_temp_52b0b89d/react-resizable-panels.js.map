{"version": 3, "sources": ["../../react-resizable-panels/dist/react-resizable-panels.browser.development.js"], "sourcesContent": ["import * as React from 'react';\nimport { createContext, useLayoutEffect, useRef, forwardRef, createElement, useContext, useImperativeHandle, useState, useCallback, useEffect, useMemo } from 'react';\n\n// The \"contextmenu\" event is not supported as a PointerEvent in all browsers yet, so MouseEvent still need to be handled\n\nconst PanelGroupContext = createContext(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst DATA_ATTRIBUTES = {\n  group: \"data-panel-group\",\n  groupDirection: \"data-panel-group-direction\",\n  groupId: \"data-panel-group-id\",\n  panel: \"data-panel\",\n  panelCollapsible: \"data-panel-collapsible\",\n  panelId: \"data-panel-id\",\n  panelSize: \"data-panel-size\",\n  resizeHandle: \"data-resize-handle\",\n  resizeHandleActive: \"data-resize-handle-active\",\n  resizeHandleEnabled: \"data-panel-resize-handle-enabled\",\n  resizeHandleId: \"data-panel-resize-handle-id\",\n  resizeHandleState: \"data-resize-handle-state\"\n};\nconst PRECISION = 10;\n\nconst useIsomorphicLayoutEffect = useLayoutEffect ;\n\nconst useId = React[\"useId\".toString()];\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = useRef(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSize,\n  collapsible,\n  defaultSize,\n  forwardedRef,\n  id: idFromProps,\n  maxSize,\n  minSize,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const context = useContext(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    reevaluatePanelConstraints,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = useRef({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSize,\n      collapsible,\n      defaultSize,\n      maxSize,\n      minSize\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = useRef({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) ;\n  }\n  useIsomorphicLayoutEffect(() => {\n    const {\n      callbacks,\n      constraints\n    } = panelDataRef.current;\n    const prevConstraints = {\n      ...constraints\n    };\n    panelDataRef.current.id = panelId;\n    panelDataRef.current.idIsFromProps = idFromProps !== undefined;\n    panelDataRef.current.order = order;\n    callbacks.onCollapse = onCollapse;\n    callbacks.onExpand = onExpand;\n    callbacks.onResize = onResize;\n    constraints.collapsedSize = collapsedSize;\n    constraints.collapsible = collapsible;\n    constraints.defaultSize = defaultSize;\n    constraints.maxSize = maxSize;\n    constraints.minSize = minSize;\n\n    // If constraints have changed, we should revisit panel sizes.\n    // This is uncommon but may happen if people are trying to implement pixel based constraints.\n    if (prevConstraints.collapsedSize !== constraints.collapsedSize || prevConstraints.collapsible !== constraints.collapsible || prevConstraints.maxSize !== constraints.maxSize || prevConstraints.minSize !== constraints.minSize) {\n      reevaluatePanelConstraints(panelDataRef.current, prevConstraints);\n    }\n  });\n  useIsomorphicLayoutEffect(() => {\n    const panelData = panelDataRef.current;\n    registerPanel(panelData);\n    return () => {\n      unregisterPanel(panelData);\n    };\n  }, [order, panelId, registerPanel, unregisterPanel]);\n  useImperativeHandle(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: minSize => {\n      expandPanel(panelDataRef.current, minSize);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: size => {\n      resizePanel(panelDataRef.current, size);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current, defaultSize);\n  return createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: panelId,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.panel]: \"\",\n    [DATA_ATTRIBUTES.panelCollapsible]: collapsible || undefined,\n    [DATA_ATTRIBUTES.panelId]: panelId,\n    [DATA_ATTRIBUTES.panelSize]: parseFloat(\"\" + style.flexGrow).toFixed(1)\n  });\n}\nconst Panel = forwardRef((props, ref) => createElement(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nlet nonce;\nfunction getNonce() {\n  return nonce;\n}\nfunction setNonce(value) {\n  nonce = value;\n}\n\nlet currentCursorStyle = null;\nlet enabled = true;\nlet prevRuleIndex = -1;\nlet styleElement = null;\nfunction disableGlobalCursorStyles() {\n  enabled = false;\n}\nfunction enableGlobalCursorStyles() {\n  enabled = true;\n}\nfunction getCursorStyle(state, constraintFlags) {\n  if (constraintFlags) {\n    const horizontalMin = (constraintFlags & EXCEEDED_HORIZONTAL_MIN) !== 0;\n    const horizontalMax = (constraintFlags & EXCEEDED_HORIZONTAL_MAX) !== 0;\n    const verticalMin = (constraintFlags & EXCEEDED_VERTICAL_MIN) !== 0;\n    const verticalMax = (constraintFlags & EXCEEDED_VERTICAL_MAX) !== 0;\n    if (horizontalMin) {\n      if (verticalMin) {\n        return \"se-resize\";\n      } else if (verticalMax) {\n        return \"ne-resize\";\n      } else {\n        return \"e-resize\";\n      }\n    } else if (horizontalMax) {\n      if (verticalMin) {\n        return \"sw-resize\";\n      } else if (verticalMax) {\n        return \"nw-resize\";\n      } else {\n        return \"w-resize\";\n      }\n    } else if (verticalMin) {\n      return \"s-resize\";\n    } else if (verticalMax) {\n      return \"n-resize\";\n    }\n  }\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"intersection\":\n      return \"move\";\n    case \"vertical\":\n      return \"ns-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (styleElement !== null) {\n    document.head.removeChild(styleElement);\n    currentCursorStyle = null;\n    styleElement = null;\n    prevRuleIndex = -1;\n  }\n}\nfunction setGlobalCursorStyle(state, constraintFlags) {\n  var _styleElement$sheet$i, _styleElement$sheet2;\n  if (!enabled) {\n    return;\n  }\n  const style = getCursorStyle(state, constraintFlags);\n  if (currentCursorStyle === style) {\n    return;\n  }\n  currentCursorStyle = style;\n  if (styleElement === null) {\n    styleElement = document.createElement(\"style\");\n    const nonce = getNonce();\n    if (nonce) {\n      styleElement.setAttribute(\"nonce\", nonce);\n    }\n    document.head.appendChild(styleElement);\n  }\n  if (prevRuleIndex >= 0) {\n    var _styleElement$sheet;\n    (_styleElement$sheet = styleElement.sheet) === null || _styleElement$sheet === void 0 ? void 0 : _styleElement$sheet.removeRule(prevRuleIndex);\n  }\n  prevRuleIndex = (_styleElement$sheet$i = (_styleElement$sheet2 = styleElement.sheet) === null || _styleElement$sheet2 === void 0 ? void 0 : _styleElement$sheet2.insertRule(`*{cursor: ${style} !important;}`)) !== null && _styleElement$sheet$i !== void 0 ? _styleElement$sheet$i : -1;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isPointerEvent(event) {\n  return event.type.startsWith(\"pointer\");\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\n\nfunction getResizeEventCoordinates(event) {\n  if (isPointerEvent(event)) {\n    if (event.isPrimary) {\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    }\n  } else if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return {\n    x: Infinity,\n    y: Infinity\n  };\n}\n\nfunction getInputType() {\n  if (typeof matchMedia === \"function\") {\n    return matchMedia(\"(pointer:coarse)\").matches ? \"coarse\" : \"fine\";\n  }\n}\n\nfunction intersects(rectOne, rectTwo, strict) {\n  if (strict) {\n    return rectOne.x < rectTwo.x + rectTwo.width && rectOne.x + rectOne.width > rectTwo.x && rectOne.y < rectTwo.y + rectTwo.height && rectOne.y + rectOne.height > rectTwo.y;\n  } else {\n    return rectOne.x <= rectTwo.x + rectTwo.width && rectOne.x + rectOne.width >= rectTwo.x && rectOne.y <= rectTwo.y + rectTwo.height && rectOne.y + rectOne.height >= rectTwo.y;\n  }\n}\n\n// Forked from NPM stacking-order@2.0.0\n\n/**\n * Determine which of two nodes appears in front of the other —\n * if `a` is in front, returns 1, otherwise returns -1\n * @param {HTMLElement | SVGElement} a\n * @param {HTMLElement | SVGElement} b\n */\nfunction compare(a, b) {\n  if (a === b) throw new Error(\"Cannot compare node with itself\");\n  const ancestors = {\n    a: get_ancestors(a),\n    b: get_ancestors(b)\n  };\n  let common_ancestor;\n\n  // remove shared ancestors\n  while (ancestors.a.at(-1) === ancestors.b.at(-1)) {\n    a = ancestors.a.pop();\n    b = ancestors.b.pop();\n    common_ancestor = a;\n  }\n  assert(common_ancestor, \"Stacking order can only be calculated for elements with a common ancestor\");\n  const z_indexes = {\n    a: get_z_index(find_stacking_context(ancestors.a)),\n    b: get_z_index(find_stacking_context(ancestors.b))\n  };\n  if (z_indexes.a === z_indexes.b) {\n    const children = common_ancestor.childNodes;\n    const furthest_ancestors = {\n      a: ancestors.a.at(-1),\n      b: ancestors.b.at(-1)\n    };\n    let i = children.length;\n    while (i--) {\n      const child = children[i];\n      if (child === furthest_ancestors.a) return 1;\n      if (child === furthest_ancestors.b) return -1;\n    }\n  }\n  return Math.sign(z_indexes.a - z_indexes.b);\n}\nconst props = /\\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\\b/;\n\n/** @param {HTMLElement | SVGElement} node */\nfunction is_flex_item(node) {\n  var _get_parent;\n  // @ts-ignore\n  const display = getComputedStyle((_get_parent = get_parent(node)) !== null && _get_parent !== void 0 ? _get_parent : node).display;\n  return display === \"flex\" || display === \"inline-flex\";\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction creates_stacking_context(node) {\n  const style = getComputedStyle(node);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context\n  if (style.position === \"fixed\") return true;\n  // Forked to fix upstream bug https://github.com/Rich-Harris/stacking-order/issues/3\n  // if (\n  //   (style.zIndex !== \"auto\" && style.position !== \"static\") ||\n  //   is_flex_item(node)\n  // )\n  if (style.zIndex !== \"auto\" && (style.position !== \"static\" || is_flex_item(node))) return true;\n  if (+style.opacity < 1) return true;\n  if (\"transform\" in style && style.transform !== \"none\") return true;\n  if (\"webkitTransform\" in style && style.webkitTransform !== \"none\") return true;\n  if (\"mixBlendMode\" in style && style.mixBlendMode !== \"normal\") return true;\n  if (\"filter\" in style && style.filter !== \"none\") return true;\n  if (\"webkitFilter\" in style && style.webkitFilter !== \"none\") return true;\n  if (\"isolation\" in style && style.isolation === \"isolate\") return true;\n  if (props.test(style.willChange)) return true;\n  // @ts-expect-error\n  if (style.webkitOverflowScrolling === \"touch\") return true;\n  return false;\n}\n\n/** @param {(HTMLElement| SVGElement)[]} nodes */\nfunction find_stacking_context(nodes) {\n  let i = nodes.length;\n  while (i--) {\n    const node = nodes[i];\n    assert(node, \"Missing node\");\n    if (creates_stacking_context(node)) return node;\n  }\n  return null;\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction get_z_index(node) {\n  return node && Number(getComputedStyle(node).zIndex) || 0;\n}\n\n/** @param {HTMLElement} node */\nfunction get_ancestors(node) {\n  const ancestors = [];\n  while (node) {\n    ancestors.push(node);\n    // @ts-ignore\n    node = get_parent(node);\n  }\n  return ancestors; // [ node, ... <body>, <html>, document ]\n}\n\n/** @param {HTMLElement} node */\nfunction get_parent(node) {\n  const {\n    parentNode\n  } = node;\n  if (parentNode && parentNode instanceof ShadowRoot) {\n    return parentNode.host;\n  }\n  return parentNode;\n}\n\nconst EXCEEDED_HORIZONTAL_MIN = 0b0001;\nconst EXCEEDED_HORIZONTAL_MAX = 0b0010;\nconst EXCEEDED_VERTICAL_MIN = 0b0100;\nconst EXCEEDED_VERTICAL_MAX = 0b1000;\nconst isCoarsePointer = getInputType() === \"coarse\";\nlet intersectingHandles = [];\nlet isPointerDown = false;\nlet ownerDocumentCounts = new Map();\nlet panelConstraintFlags = new Map();\nconst registeredResizeHandlers = new Set();\nfunction registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {\n  var _ownerDocumentCounts$;\n  const {\n    ownerDocument\n  } = element;\n  const data = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState\n  };\n  const count = (_ownerDocumentCounts$ = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$ !== void 0 ? _ownerDocumentCounts$ : 0;\n  ownerDocumentCounts.set(ownerDocument, count + 1);\n  registeredResizeHandlers.add(data);\n  updateListeners();\n  return function unregisterResizeHandle() {\n    var _ownerDocumentCounts$2;\n    panelConstraintFlags.delete(resizeHandleId);\n    registeredResizeHandlers.delete(data);\n    const count = (_ownerDocumentCounts$2 = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$2 !== void 0 ? _ownerDocumentCounts$2 : 1;\n    ownerDocumentCounts.set(ownerDocument, count - 1);\n    updateListeners();\n    if (count === 1) {\n      ownerDocumentCounts.delete(ownerDocument);\n    }\n\n    // If the resize handle that is currently unmounting is intersecting with the pointer,\n    // update the global pointer to account for the change\n    if (intersectingHandles.includes(data)) {\n      const index = intersectingHandles.indexOf(data);\n      if (index >= 0) {\n        intersectingHandles.splice(index, 1);\n      }\n      updateCursor();\n\n      // Also instruct the handle to stop dragging; this prevents the parent group from being left in an inconsistent state\n      // See github.com/bvaughn/react-resizable-panels/issues/402\n      setResizeHandlerState(\"up\", true, null);\n    }\n  };\n}\nfunction handlePointerDown(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  isPointerDown = true;\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateListeners();\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates(\"down\", event);\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n}\nfunction handlePointerMove(event) {\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n\n  // Edge case (see #340)\n  // Detect when the pointer has been released outside an iframe on a different domain\n  if (isPointerDown && event.buttons === 0) {\n    isPointerDown = false;\n    updateResizeHandlerStates(\"up\", event);\n  }\n  if (!isPointerDown) {\n    const {\n      target\n    } = event;\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({\n      target,\n      x,\n      y\n    });\n  }\n  updateResizeHandlerStates(\"move\", event);\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor();\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n}\nfunction handlePointerUp(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  panelConstraintFlags.clear();\n  isPointerDown = false;\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n  updateResizeHandlerStates(\"up\", event);\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateCursor();\n  updateListeners();\n}\nfunction isWithinResizeHandle(element) {\n  let currentElement = element;\n  while (currentElement) {\n    if (currentElement.hasAttribute(DATA_ATTRIBUTES.resizeHandle)) {\n      return true;\n    }\n    currentElement = currentElement.parentElement;\n  }\n  return false;\n}\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y\n}) {\n  intersectingHandles.splice(0);\n  let targetElement = null;\n  if (target instanceof HTMLElement || target instanceof SVGElement) {\n    targetElement = target;\n  }\n  registeredResizeHandlers.forEach(data => {\n    const {\n      element: dragHandleElement,\n      hitAreaMargins\n    } = data;\n    const dragHandleRect = dragHandleElement.getBoundingClientRect();\n    const {\n      bottom,\n      left,\n      right,\n      top\n    } = dragHandleRect;\n    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;\n    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (targetElement !== null && document.contains(targetElement) && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) &&\n      // Calculating stacking order has a cost, so we should avoid it if possible\n      // That is why we only check potentially intersecting handles,\n      // and why we skip if the event target is within the handle's DOM\n      compare(targetElement, dragHandleElement) > 0) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement = targetElement;\n        let didIntersect = false;\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break;\n          } else if (intersects(currentElement.getBoundingClientRect(), dragHandleRect, true)) {\n            didIntersect = true;\n            break;\n          }\n          currentElement = currentElement.parentElement;\n        }\n        if (didIntersect) {\n          return;\n        }\n      }\n      intersectingHandles.push(data);\n    }\n  });\n}\nfunction reportConstraintsViolation(resizeHandleId, flag) {\n  panelConstraintFlags.set(resizeHandleId, flag);\n}\nfunction updateCursor() {\n  let intersectsHorizontal = false;\n  let intersectsVertical = false;\n  intersectingHandles.forEach(data => {\n    const {\n      direction\n    } = data;\n    if (direction === \"horizontal\") {\n      intersectsHorizontal = true;\n    } else {\n      intersectsVertical = true;\n    }\n  });\n  let constraintFlags = 0;\n  panelConstraintFlags.forEach(flag => {\n    constraintFlags |= flag;\n  });\n  if (intersectsHorizontal && intersectsVertical) {\n    setGlobalCursorStyle(\"intersection\", constraintFlags);\n  } else if (intersectsHorizontal) {\n    setGlobalCursorStyle(\"horizontal\", constraintFlags);\n  } else if (intersectsVertical) {\n    setGlobalCursorStyle(\"vertical\", constraintFlags);\n  } else {\n    resetGlobalCursorStyle();\n  }\n}\nlet listenersAbortController;\nfunction updateListeners() {\n  var _listenersAbortContro;\n  (_listenersAbortContro = listenersAbortController) === null || _listenersAbortContro === void 0 ? void 0 : _listenersAbortContro.abort();\n  listenersAbortController = new AbortController();\n  const options = {\n    capture: true,\n    signal: listenersAbortController.signal\n  };\n  if (!registeredResizeHandlers.size) {\n    return;\n  }\n  if (isPointerDown) {\n    if (intersectingHandles.length > 0) {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const {\n          body\n        } = ownerDocument;\n        if (count > 0) {\n          body.addEventListener(\"contextmenu\", handlePointerUp, options);\n          body.addEventListener(\"pointerleave\", handlePointerMove, options);\n          body.addEventListener(\"pointermove\", handlePointerMove, options);\n        }\n      });\n    }\n    ownerDocumentCounts.forEach((_, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      body.addEventListener(\"pointerup\", handlePointerUp, options);\n      body.addEventListener(\"pointercancel\", handlePointerUp, options);\n    });\n  } else {\n    ownerDocumentCounts.forEach((count, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      if (count > 0) {\n        body.addEventListener(\"pointerdown\", handlePointerDown, options);\n        body.addEventListener(\"pointermove\", handlePointerMove, options);\n      }\n    });\n  }\n}\nfunction updateResizeHandlerStates(action, event) {\n  registeredResizeHandlers.forEach(data => {\n    const {\n      setResizeHandlerState\n    } = data;\n    const isActive = intersectingHandles.includes(data);\n    setResizeHandlerState(action, isActive, event);\n  });\n}\n\nfunction useForceUpdate() {\n  const [_, setCount] = useState(0);\n  return useCallback(() => setCount(prevCount => prevCount + 1), []);\n}\n\nfunction assert(expectedCondition, message) {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  if (actual.toFixed(fractionDigits) === expected.toFixed(fractionDigits)) {\n    return 0;\n  } else {\n    return actual > expected ? 1 : -1;\n  }\n}\nfunction fuzzyNumbersEqual$1(actual, expected, fractionDigits = PRECISION) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyLayoutsEqual(actual, expected, fractionDigits) {\n  if (actual.length !== expected.length) {\n    return false;\n  }\n  for (let index = 0; index < actual.length; index++) {\n    const actualSize = actual[index];\n    const expectedSize = expected[index];\n    if (!fuzzyNumbersEqual(actualSize, expectedSize, fractionDigits)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  assert(panelConstraints != null, `Panel constraints not found for index ${panelIndex}`);\n  let {\n    collapsedSize = 0,\n    collapsible,\n    maxSize = 100,\n    minSize = 0\n  } = panelConstraints;\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2;\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n        size = collapsedSize;\n      } else {\n        size = minSize;\n      }\n    } else {\n      size = minSize;\n    }\n  }\n  size = Math.min(maxSize, size);\n  size = parseFloat(size.toFixed(PRECISION));\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  initialLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  prevLayout,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return initialLayout;\n  }\n  const nextLayout = [...initialLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  assert(firstPivotIndex != null, \"Invalid first pivot index\");\n  assert(secondPivotIndex != null, \"Invalid second pivot index\");\n  let deltaApplied = 0;\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta()`);\n  // DEBUG.push(`  initialLayout: ${initialLayout.join(\", \")}`);\n  // DEBUG.push(`  prevLayout: ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `Panel constraints not found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `No panel constraints found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraintsArray.length) {\n        break;\n      }\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyLayoutsEqual(prevLayout, nextLayout)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = initialLayout[pivotIndex];\n    assert(prevSize != null, `Previous layout not found for panel index ${pivotIndex}`);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index];\n        assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  // DEBUG.push(`total size: ${totalSize}`);\n\n  // If our new layout doesn't add up to 100%, that means the requested delta can't be applied\n  // In that case, fall back to our most recent valid layout\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n\n  // console.log(DEBUG.join(\"\\n\"));\n  return nextLayout;\n}\n\nfunction calculateAriaValues({\n  layout,\n  panelsArray,\n  pivotIndices\n}) {\n  let currentMinSize = 0;\n  let currentMaxSize = 100;\n  let totalMinSize = 0;\n  let totalMaxSize = 0;\n  const firstIndex = pivotIndices[0];\n  assert(firstIndex != null, \"No pivot index found\");\n\n  // A panel's effective min/max sizes also need to account for other panel's sizes.\n  panelsArray.forEach((panelData, index) => {\n    const {\n      constraints\n    } = panelData;\n    const {\n      maxSize = 100,\n      minSize = 0\n    } = constraints;\n    if (index === firstIndex) {\n      currentMinSize = minSize;\n      currentMaxSize = maxSize;\n    } else {\n      totalMinSize += minSize;\n      totalMaxSize += maxSize;\n    }\n  });\n  const valueMax = Math.min(currentMaxSize, 100 - totalMinSize);\n  const valueMin = Math.max(currentMinSize, 100 - totalMaxSize);\n  const valueNow = layout[firstIndex];\n  return {\n    valueMax,\n    valueMin,\n    valueNow\n  };\n}\n\nfunction getResizeHandleElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[${DATA_ATTRIBUTES.resizeHandleId}][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id, scope = document) {\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handles.findIndex(handle => handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId) === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId, panelGroupElement) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId, panelGroupElement);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction isHTMLElement(target) {\n  if (target instanceof HTMLElement) {\n    return true;\n  }\n\n  // Fallback to duck typing to handle edge case of portals within a popup window\n  return typeof target === \"object\" && target !== null && \"tagName\" in target && \"getAttribute\" in target;\n}\n\nfunction getPanelGroupElement(id, rootElement = document) {\n  // If the root element is the PanelGroup\n  if (isHTMLElement(rootElement) && rootElement.dataset.panelGroupId == id) {\n    return rootElement;\n  }\n\n  // Else query children\n  const element = rootElement.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandleElement(id, scope = document) {\n  const element = scope.querySelector(`[${DATA_ATTRIBUTES.resizeHandleId}=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray, scope = document) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId, scope);\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout\n}) {\n  const devWarningsRef = useRef({\n    didWarnAboutMissingResizeHandle: false\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const resizeHandleElements = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    for (let index = 0; index < panelDataArray.length - 1; index++) {\n      const {\n        valueMax,\n        valueMin,\n        valueNow\n      } = calculateAriaValues({\n        layout,\n        panelsArray: panelDataArray,\n        pivotIndices: [index, index + 1]\n      });\n      const resizeHandleElement = resizeHandleElements[index];\n      if (resizeHandleElement == null) {\n        {\n          const {\n            didWarnAboutMissingResizeHandle\n          } = devWarningsRef.current;\n          if (!didWarnAboutMissingResizeHandle) {\n            devWarningsRef.current.didWarnAboutMissingResizeHandle = true;\n            console.warn(`WARNING: Missing resize handle for PanelGroup \"${groupId}\"`);\n          }\n        }\n      } else {\n        const panelData = panelDataArray[index];\n        assert(panelData, `No panel data found for index \"${index}\"`);\n        resizeHandleElement.setAttribute(\"aria-controls\", panelData.id);\n        resizeHandleElement.setAttribute(\"aria-valuemax\", \"\" + Math.round(valueMax));\n        resizeHandleElement.setAttribute(\"aria-valuemin\", \"\" + Math.round(valueMin));\n        resizeHandleElement.setAttribute(\"aria-valuenow\", valueNow != null ? \"\" + Math.round(valueNow) : \"\");\n      }\n    }\n    return () => {\n      resizeHandleElements.forEach((resizeHandleElement, index) => {\n        resizeHandleElement.removeAttribute(\"aria-controls\");\n        resizeHandleElement.removeAttribute(\"aria-valuemax\");\n        resizeHandleElement.removeAttribute(\"aria-valuemin\");\n        resizeHandleElement.removeAttribute(\"aria-valuenow\");\n      });\n    };\n  }, [groupId, layout, panelDataArray, panelGroupElement]);\n  useEffect(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const eagerValues = eagerValuesRef.current;\n    assert(eagerValues, `Eager values not found`);\n    const {\n      panelDataArray\n    } = eagerValues;\n    const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    assert(handles, `No resize handles found for group id \"${groupId}\"`);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId);\n      assert(handleId, `Resize handle element has no handle id attribute`);\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray, panelGroupElement);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                assert(panelData, `No panel data found for index ${index}`);\n                const size = layout[index];\n                const {\n                  collapsedSize = 0,\n                  collapsible,\n                  minSize = 0\n                } = panelData.constraints;\n                if (size != null && collapsible) {\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    initialLayout: layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId, panelGroupElement),\n                    prevLayout: layout,\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [panelGroupElement, committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  return isHorizontal ? x : y;\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement);\n  assert(handleElement, `No resize handle element found for id \"${dragHandleId}\"`);\n  const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n  assert(groupId, `Resize handle element has no group id attribute`);\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n  assert(groupElement, `No group element found for id \"${groupId}\"`);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, dragHandleId, direction, initialDragState, keyboardResizeBy, panelGroupElement) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null) {\n      return 0;\n    }\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement);\n  }\n}\n\nfunction calculateUnsafeDefaultLayout({\n  panelDataArray\n}) {\n  const layout = Array(panelDataArray.length);\n  const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n  let numPanelsWithSizes = 0;\n  let remainingSize = 100;\n\n  // Distribute default sizes first\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const panelConstraints = panelConstraintsArray[index];\n    assert(panelConstraints, `Panel constraints not found for index ${index}`);\n    const {\n      defaultSize\n    } = panelConstraints;\n    if (defaultSize != null) {\n      numPanelsWithSizes++;\n      layout[index] = defaultSize;\n      remainingSize -= defaultSize;\n    }\n  }\n\n  // Remaining size should be distributed evenly between panels without default sizes\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const panelConstraints = panelConstraintsArray[index];\n    assert(panelConstraints, `Panel constraints not found for index ${index}`);\n    const {\n      defaultSize\n    } = panelConstraints;\n    if (defaultSize != null) {\n      continue;\n    }\n    const numRemainingPanels = panelDataArray.length - numPanelsWithSizes;\n    const size = remainingSize / numRemainingPanels;\n    numPanelsWithSizes++;\n    layout[index] = size;\n    remainingSize -= size;\n  }\n  return layout;\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(panelsArray, layout, panelIdToLastNotifiedSizeMap) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index];\n    assert(panelData, `Panel data not found for index ${index}`);\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsedSize = 0,\n      collapsible\n    } = constraints;\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId];\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(size, lastNotifiedSize);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        if (onExpand && (lastNotifiedSize == null || fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && !fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedSize == null || !fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  defaultSize,\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (size == null) {\n    // Initial render (before panels have registered themselves)\n    // In order to support server rendering, fall back to default size if provided\n    flexGrow = defaultSize != undefined ? defaultSize.toPrecision(precision) : \"1\";\n  } else if (panelData.length === 1) {\n    // Special case: Single panel group should always fill full width/height\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\nfunction getPanelGroupKey(autoSaveId) {\n  return `react-resizable-panels:${autoSaveId}`;\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getPanelKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return order ? `${order}:${JSON.stringify(constraints)}` : JSON.stringify(constraints);\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const panelGroupKey = getPanelGroupKey(autoSaveId);\n    const serialized = storage.getItem(panelGroupKey);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction loadPanelGroupState(autoSaveId, panels, storage) {\n  var _loadSerializedPanelG, _state$panelKey;\n  const state = (_loadSerializedPanelG = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG !== void 0 ? _loadSerializedPanelG : {};\n  const panelKey = getPanelKey(panels);\n  return (_state$panelKey = state[panelKey]) !== null && _state$panelKey !== void 0 ? _state$panelKey : null;\n}\nfunction savePanelGroupState(autoSaveId, panels, panelSizesBeforeCollapse, sizes, storage) {\n  var _loadSerializedPanelG2;\n  const panelGroupKey = getPanelGroupKey(autoSaveId);\n  const panelKey = getPanelKey(panels);\n  const state = (_loadSerializedPanelG2 = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG2 !== void 0 ? _loadSerializedPanelG2 : {};\n  state[panelKey] = {\n    expandToSizes: Object.fromEntries(panelSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(panelGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    const panelConstraints = panelConstraintsArray[panelIndex];\n    assert(panelConstraints, `No panel constraints found for index ${panelIndex}`);\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0\n    } = panelConstraints;\n    if (minSize > maxSize) {\n      warnings.push(`min size (${minSize}%) should not be greater than max size (${maxSize}%)`);\n    }\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push(\"default size should not be less than 0\");\n      } else if (defaultSize < minSize && (!collapsible || defaultSize !== collapsedSize)) {\n        warnings.push(\"default size should not be less than min size\");\n      }\n      if (defaultSize > 100) {\n        warnings.push(\"default size should not be greater than 100\");\n      } else if (defaultSize > maxSize) {\n        warnings.push(\"default size should not be greater than max size\");\n      }\n    }\n    if (collapsedSize > minSize) {\n      warnings.push(\"collapsed size should not be greater than min size\");\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100) && nextLayout.length > 0) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}. Layout normalization will be applied.`);\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null, `No layout data found for index ${index}`);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null, `No layout data found for index ${index}`);\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null, `No layout data found for index ${index}`);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  direction,\n  forwardedRef,\n  id: idFromProps = null,\n  onLayout = null,\n  keyboardResizeBy = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const panelGroupElementRef = useRef(null);\n  const [dragState, setDragState] = useState(null);\n  const [layout, setLayout] = useState([]);\n  const forceUpdate = useForceUpdate();\n  const panelIdToLastNotifiedSizeMapRef = useRef({});\n  const panelSizeBeforeCollapseRef = useRef(new Map());\n  const prevDeltaRef = useRef(0);\n  const committedValuesRef = useRef({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeBy,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = useRef({\n    layout,\n    panelDataArray: [],\n    panelDataArrayChanged: false\n  });\n  const devWarningsRef = useRef({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  useImperativeHandle(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        layout\n      } = eagerValuesRef.current;\n      return layout;\n    },\n    setLayout: unsafeLayout => {\n      const {\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const safeLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout);\n        }\n        callPanelCallbacks(panelDataArray, safeLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  }), []);\n  useIsomorphicLayoutEffect(() => {\n    committedValuesRef.current.autoSaveId = autoSaveId;\n    committedValuesRef.current.direction = direction;\n    committedValuesRef.current.dragState = dragState;\n    committedValuesRef.current.id = groupId;\n    committedValuesRef.current.onLayout = onLayout;\n    committedValuesRef.current.storage = storage;\n  });\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout,\n    panelGroupElement: panelGroupElementRef.current\n  });\n  useEffect(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n      let debouncedSave = debounceMap[autoSaveId];\n\n      // Limit the frequency of localStorage updates.\n      if (debouncedSave == null) {\n        debouncedSave = debounce(savePanelGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n      }\n\n      // Clone mutable data before passing to the debounced function,\n      // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n      const clonedPanelDataArray = [...panelDataArray];\n      const clonedPanelSizesBeforeCollapse = new Map(panelSizeBeforeCollapseRef.current);\n      debouncedSave(autoSaveId, clonedPanelDataArray, clonedPanelSizesBeforeCollapse, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n\n  // DEV warnings\n  useEffect(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const panelData = panelDataArray[panelIndex];\n          assert(panelData, `Panel data not found for index ${panelIndex}`);\n          const isValid = validatePanelConstraints({\n            panelConstraints,\n            panelId: panelData.id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = useCallback(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n      if (!fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSize);\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - collapsedSize : collapsedSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = useCallback((panelData, minSizeOverride) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize = 0,\n        minSize: minSizeFromProps = 0,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      const minSize = minSizeOverride !== null && minSizeOverride !== void 0 ? minSizeOverride : minSizeFromProps;\n      if (fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSize = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSize = prevPanelSize != null && prevPanelSize >= minSize ? prevPanelSize : minSize;\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return panelSize;\n  }, []);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = useCallback((panelData, defaultSize) => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n    return computePanelFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return collapsible === true && fuzzyNumbersEqual$1(panelSize, collapsedSize);\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return !collapsible || fuzzyCompareNumbers(panelSize, collapsedSize) > 0;\n  }, []);\n  const registerPanel = useCallback(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    eagerValuesRef.current.panelDataArrayChanged = true;\n    forceUpdate();\n  }, [forceUpdate]);\n\n  // (Re)calculate group layout whenever panels are registered or unregistered.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useIsomorphicLayoutEffect(() => {\n    if (eagerValuesRef.current.panelDataArrayChanged) {\n      eagerValuesRef.current.panelDataArrayChanged = false;\n      const {\n        autoSaveId,\n        onLayout,\n        storage\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n\n      // If this panel has been configured to persist sizing information,\n      // default size should be restored from local storage if possible.\n      let unsafeLayout = null;\n      if (autoSaveId) {\n        const state = loadPanelGroupState(autoSaveId, panelDataArray, storage);\n        if (state) {\n          panelSizeBeforeCollapseRef.current = new Map(Object.entries(state.expandToSizes));\n          unsafeLayout = state.layout;\n        }\n      }\n      if (unsafeLayout == null) {\n        unsafeLayout = calculateUnsafeDefaultLayout({\n          panelDataArray\n        });\n      }\n\n      // Validate even saved layouts in case something has changed since last render\n      // e.g. for pixel groups, this could be the size of the window\n      const nextLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, nextLayout)) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  });\n\n  // Reset the cached layout if hidden by the Activity/Offscreen API\n  useIsomorphicLayoutEffect(() => {\n    const eagerValues = eagerValuesRef.current;\n    return () => {\n      eagerValues.layout = [];\n    };\n  }, []);\n  const registerResizeHandle = useCallback(dragHandleId => {\n    let isRTL = false;\n    const panelGroupElement = panelGroupElementRef.current;\n    if (panelGroupElement) {\n      const style = window.getComputedStyle(panelGroupElement, null);\n      if (style.getPropertyValue(\"direction\") === \"rtl\") {\n        isRTL = true;\n      }\n    }\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const panelGroupElement = panelGroupElementRef.current;\n      if (!panelGroupElement) {\n        return () => null;\n      }\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeBy,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId, panelGroupElement);\n      let delta = calculateDeltaPercentage(event, dragHandleId, direction, dragState, keyboardResizeBy, panelGroupElement);\n      const isHorizontal = direction === \"horizontal\";\n      if (isHorizontal && isRTL) {\n        delta = -delta;\n      }\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        initialLayout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        prevLayout,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isPointerEvent(event) || isMouseEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged && delta !== 0) {\n            // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n            // This mimics VS Code behavior.\n            if (isHorizontal) {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX);\n            } else {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX);\n            }\n          } else {\n            reportConstraintsViolation(dragHandleId, 0);\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = useCallback((panelData, unsafePanelSize) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      panelSize,\n      pivotIndices\n    } = panelDataHelper(panelDataArray, panelData, prevLayout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSize - unsafePanelSize : unsafePanelSize - panelSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      initialLayout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      prevLayout,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout);\n      }\n      callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n    }\n  }, []);\n  const reevaluatePanelConstraints = useCallback((panelData, prevConstraints) => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize: prevCollapsedSize = 0,\n      collapsible: prevCollapsible\n    } = prevConstraints;\n    const {\n      collapsedSize: nextCollapsedSize = 0,\n      collapsible: nextCollapsible,\n      maxSize: nextMaxSize = 100,\n      minSize: nextMinSize = 0\n    } = panelData.constraints;\n    const {\n      panelSize: prevPanelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    if (prevPanelSize == null) {\n      // It's possible that the panels in this group have changed since the last render\n      return;\n    }\n    if (prevCollapsible && nextCollapsible && fuzzyNumbersEqual$1(prevPanelSize, prevCollapsedSize)) {\n      if (!fuzzyNumbersEqual$1(prevCollapsedSize, nextCollapsedSize)) {\n        resizePanel(panelData, nextCollapsedSize);\n      }\n    } else if (prevPanelSize < nextMinSize) {\n      resizePanel(panelData, nextMinSize);\n    } else if (prevPanelSize > nextMaxSize) {\n      resizePanel(panelData, nextMaxSize);\n    }\n  }, [resizePanel]);\n\n  // TODO Multiple drag handles can be active at the same time so this API is a bit awkward now\n  const startDragging = useCallback((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    if (!panelGroupElementRef.current) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(dragHandleId, panelGroupElementRef.current);\n    assert(handleElement, `Drag handle element not found for id \"${dragHandleId}\"`);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = useCallback(() => {\n    setDragState(null);\n  }, []);\n  const unregisterPanel = useCallback(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = findPanelDataIndex(panelDataArray, panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n\n      // TRICKY\n      // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n      // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n      // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n      delete panelIdToLastNotifiedSizeMapRef.current[panelData.id];\n      eagerValuesRef.current.panelDataArrayChanged = true;\n      forceUpdate();\n    }\n  }, [forceUpdate]);\n  const context = useMemo(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    reevaluatePanelConstraints,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel,\n    panelGroupElement: panelGroupElementRef.current\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, reevaluatePanelConstraints, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return createElement(PanelGroupContext.Provider, {\n    value: context\n  }, createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    ref: panelGroupElementRef,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.group]: \"\",\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId\n  }));\n}\nconst PanelGroup = forwardRef((props, ref) => createElement(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction findPanelDataIndex(panelDataArray, panelData) {\n  return panelDataArray.findIndex(prevPanelData => prevPanelData === panelData || prevPanelData.id === panelData.id);\n}\nfunction panelDataHelper(panelDataArray, panelData, layout) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSize = layout[panelIndex];\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement\n}) {\n  useEffect(() => {\n    if (disabled || resizeHandler == null || panelGroupElement == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId, panelGroupElement);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n            assert(groupId, `No group element found for id \"${groupId}\"`);\n            const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n            const index = getResizeHandleElementIndex(groupId, handleId, panelGroupElement);\n            assert(index !== null, `No resize element found for id \"${handleId}\"`);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [panelGroupElement, disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  disabled = false,\n  hitAreaMargins,\n  id: idFromProps,\n  onBlur,\n  onClick,\n  onDragging,\n  onFocus,\n  onPointerDown,\n  onPointerUp,\n  style: styleFromProps = {},\n  tabIndex = 0,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  var _hitAreaMargins$coars, _hitAreaMargins$fine;\n  const elementRef = useRef(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = useRef({\n    onClick,\n    onDragging,\n    onPointerDown,\n    onPointerUp\n  });\n  useEffect(() => {\n    callbacksRef.current.onClick = onClick;\n    callbacksRef.current.onDragging = onDragging;\n    callbacksRef.current.onPointerDown = onPointerDown;\n    callbacksRef.current.onPointerUp = onPointerUp;\n  });\n  const panelGroupContext = useContext(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    groupId,\n    registerResizeHandle: registerResizeHandleWithParentGroup,\n    startDragging,\n    stopDragging,\n    panelGroupElement\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const [state, setState] = useState(\"inactive\");\n  const [isFocused, setIsFocused] = useState(false);\n  const [resizeHandler, setResizeHandler] = useState(null);\n  const committedValuesRef = useRef({\n    state\n  });\n  useIsomorphicLayoutEffect(() => {\n    committedValuesRef.current.state = state;\n  });\n  useEffect(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandleWithParentGroup(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandleWithParentGroup]);\n\n  // Extract hit area margins before passing them to the effect's dependency array\n  // so that inline object values won't trigger re-renders\n  const coarseHitAreaMargins = (_hitAreaMargins$coars = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.coarse) !== null && _hitAreaMargins$coars !== void 0 ? _hitAreaMargins$coars : 15;\n  const fineHitAreaMargins = (_hitAreaMargins$fine = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.fine) !== null && _hitAreaMargins$fine !== void 0 ? _hitAreaMargins$fine : 5;\n  useEffect(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const element = elementRef.current;\n    assert(element, \"Element ref not attached\");\n    let didMove = false;\n    const setResizeHandlerState = (action, isActive, event) => {\n      if (!isActive) {\n        setState(\"inactive\");\n        return;\n      }\n      switch (action) {\n        case \"down\":\n          {\n            setState(\"drag\");\n            didMove = false;\n            assert(event, 'Expected event to be defined for \"down\" action');\n            startDragging(resizeHandleId, event);\n            const {\n              onDragging,\n              onPointerDown\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(true);\n            onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown();\n            break;\n          }\n        case \"move\":\n          {\n            const {\n              state\n            } = committedValuesRef.current;\n            didMove = true;\n            if (state !== \"drag\") {\n              setState(\"hover\");\n            }\n            assert(event, 'Expected event to be defined for \"move\" action');\n            resizeHandler(event);\n            break;\n          }\n        case \"up\":\n          {\n            setState(\"hover\");\n            stopDragging();\n            const {\n              onClick,\n              onDragging,\n              onPointerUp\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(false);\n            onPointerUp === null || onPointerUp === void 0 ? void 0 : onPointerUp();\n            if (!didMove) {\n              onClick === null || onClick === void 0 ? void 0 : onClick();\n            }\n            break;\n          }\n      }\n    };\n    return registerResizeHandle(resizeHandleId, element, direction, {\n      coarse: coarseHitAreaMargins,\n      fine: fineHitAreaMargins\n    }, setResizeHandlerState);\n  }, [coarseHitAreaMargins, direction, disabled, fineHitAreaMargins, registerResizeHandleWithParentGroup, resizeHandleId, resizeHandler, startDragging, stopDragging]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler,\n    panelGroupElement\n  });\n  const style = {\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    onBlur: () => {\n      setIsFocused(false);\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n    },\n    onFocus: () => {\n      setIsFocused(true);\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n    },\n    ref: elementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex,\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.resizeHandle]: \"\",\n    [DATA_ATTRIBUTES.resizeHandleActive]: state === \"drag\" ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    [DATA_ATTRIBUTES.resizeHandleEnabled]: !disabled,\n    [DATA_ATTRIBUTES.resizeHandleId]: resizeHandleId,\n    [DATA_ATTRIBUTES.resizeHandleState]: state\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nfunction usePanelGroupContext() {\n  const context = useContext(PanelGroupContext);\n  return {\n    direction: context === null || context === void 0 ? void 0 : context.direction,\n    groupId: context === null || context === void 0 ? void 0 : context.groupId\n  };\n}\n\nfunction getPanelElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getPanelElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getIntersectingRectangle(rectOne, rectTwo, strict) {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width: Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width) - Math.max(rectOne.x, rectTwo.x),\n    height: Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height) - Math.max(rectOne.y, rectTwo.y)\n  };\n}\n\nexport { DATA_ATTRIBUTES, Panel, PanelGroup, PanelResizeHandle, assert, disableGlobalCursorStyles, enableGlobalCursorStyles, getIntersectingRectangle, getPanelElement, getPanelElementsForGroup, getPanelGroupElement, getResizeHandleElement, getResizeHandleElementIndex, getResizeHandleElementsForGroup, getResizeHandlePanelIds, intersects, setNonce, usePanelGroupContext };\n"], "mappings": ";;;;;;;;AAAA,YAAuB;AACvB,mBAA8J;AAI9J,IAAM,wBAAoB,4BAAc,IAAI;AAC5C,kBAAkB,cAAc;AAEhC,IAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,OAAO;AAAA,EACP,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,mBAAmB;AACrB;AACA,IAAM,YAAY;AAElB,IAAM,4BAA4B;AAElC,IAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC;AACtC,IAAM,eAAe,OAAO,UAAU,aAAa,QAAQ,MAAM;AACjE,IAAI,UAAU;AACd,SAAS,YAAY,eAAe,MAAM;AACxC,QAAM,cAAc,aAAa;AACjC,QAAM,YAAQ,qBAAO,gBAAgB,eAAe,IAAI;AACxD,MAAI,MAAM,YAAY,MAAM;AAC1B,UAAM,UAAU,KAAK;AAAA,EACvB;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe,MAAM;AACjF;AAEA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA,WAAW,qBAAqB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,SAAS,OAAO;AAAA,EAChB,GAAG;AACL,GAAG;AACD,QAAM,cAAU,yBAAW,iBAAiB;AAC5C,MAAI,YAAY,MAAM;AACpB,UAAM,MAAM,iEAAiE;AAAA,EAC/E;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAAA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,YAAY,WAAW;AACvC,QAAM,mBAAe,qBAAO;AAAA,IAC1B,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,IAAI;AAAA,IACJ,eAAe,gBAAgB;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,QAAM,qBAAiB,qBAAO;AAAA,IAC5B,iCAAiC;AAAA,EACnC,CAAC;AAID;AACE,QAAI,CAAC,eAAe,QAAQ,gCAAiC;AAAA,EAC/D;AACA,4BAA0B,MAAM;AAC9B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,aAAa;AACjB,UAAM,kBAAkB;AAAA,MACtB,GAAG;AAAA,IACL;AACA,iBAAa,QAAQ,KAAK;AAC1B,iBAAa,QAAQ,gBAAgB,gBAAgB;AACrD,iBAAa,QAAQ,QAAQ;AAC7B,cAAU,aAAa;AACvB,cAAU,WAAW;AACrB,cAAU,WAAW;AACrB,gBAAY,gBAAgB;AAC5B,gBAAY,cAAc;AAC1B,gBAAY,cAAc;AAC1B,gBAAY,UAAU;AACtB,gBAAY,UAAU;AAItB,QAAI,gBAAgB,kBAAkB,YAAY,iBAAiB,gBAAgB,gBAAgB,YAAY,eAAe,gBAAgB,YAAY,YAAY,WAAW,gBAAgB,YAAY,YAAY,SAAS;AAChO,iCAA2B,aAAa,SAAS,eAAe;AAAA,IAClE;AAAA,EACF,CAAC;AACD,4BAA0B,MAAM;AAC9B,UAAM,YAAY,aAAa;AAC/B,kBAAc,SAAS;AACvB,WAAO,MAAM;AACX,sBAAgB,SAAS;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,OAAO,SAAS,eAAe,eAAe,CAAC;AACnD,wCAAoB,cAAc,OAAO;AAAA,IACvC,UAAU,MAAM;AACd,oBAAc,aAAa,OAAO;AAAA,IACpC;AAAA,IACA,QAAQ,CAAAC,aAAW;AACjB,kBAAY,aAAa,SAASA,QAAO;AAAA,IAC3C;AAAA,IACA,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,IACA,UAAU;AACR,aAAO,aAAa,aAAa,OAAO;AAAA,IAC1C;AAAA,IACA,cAAc;AACZ,aAAO,iBAAiB,aAAa,OAAO;AAAA,IAC9C;AAAA,IACA,aAAa;AACX,aAAO,CAAC,iBAAiB,aAAa,OAAO;AAAA,IAC/C;AAAA,IACA,QAAQ,UAAQ;AACd,MAAAD,aAAY,aAAa,SAAS,IAAI;AAAA,IACxC;AAAA,EACF,IAAI,CAAC,eAAe,aAAa,cAAc,kBAAkB,SAASA,YAAW,CAAC;AACtF,QAAM,QAAQ,cAAc,aAAa,SAAS,WAAW;AAC7D,aAAO,4BAAc,MAAM;AAAA,IACzB,GAAG;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,OAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA;AAAA,IAEA,CAAC,gBAAgB,OAAO,GAAG;AAAA,IAC3B,CAAC,gBAAgB,KAAK,GAAG;AAAA,IACzB,CAAC,gBAAgB,gBAAgB,GAAG,eAAe;AAAA,IACnD,CAAC,gBAAgB,OAAO,GAAG;AAAA,IAC3B,CAAC,gBAAgB,SAAS,GAAG,WAAW,KAAK,MAAM,QAAQ,EAAE,QAAQ,CAAC;AAAA,EACxE,CAAC;AACH;AACA,IAAM,YAAQ,yBAAW,CAACE,QAAO,YAAQ,4BAAc,uBAAuB;AAAA,EAC5E,GAAGA;AAAA,EACH,cAAc;AAChB,CAAC,CAAC;AACF,sBAAsB,cAAc;AACpC,MAAM,cAAc;AAEpB,IAAI;AACJ,SAAS,WAAW;AAClB,SAAO;AACT;AACA,SAAS,SAAS,OAAO;AACvB,UAAQ;AACV;AAEA,IAAI,qBAAqB;AACzB,IAAI,UAAU;AACd,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,SAAS,4BAA4B;AACnC,YAAU;AACZ;AACA,SAAS,2BAA2B;AAClC,YAAU;AACZ;AACA,SAAS,eAAe,OAAO,iBAAiB;AAC9C,MAAI,iBAAiB;AACnB,UAAM,iBAAiB,kBAAkB,6BAA6B;AACtE,UAAM,iBAAiB,kBAAkB,6BAA6B;AACtE,UAAM,eAAe,kBAAkB,2BAA2B;AAClE,UAAM,eAAe,kBAAkB,2BAA2B;AAClE,QAAI,eAAe;AACjB,UAAI,aAAa;AACf,eAAO;AAAA,MACT,WAAW,aAAa;AACtB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,WAAW,eAAe;AACxB,UAAI,aAAa;AACf,eAAO;AAAA,MACT,WAAW,aAAa;AACtB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,WAAW,aAAa;AACtB,aAAO;AAAA,IACT,WAAW,aAAa;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AACA,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACF;AACA,SAAS,yBAAyB;AAChC,MAAI,iBAAiB,MAAM;AACzB,aAAS,KAAK,YAAY,YAAY;AACtC,yBAAqB;AACrB,mBAAe;AACf,oBAAgB;AAAA,EAClB;AACF;AACA,SAAS,qBAAqB,OAAO,iBAAiB;AACpD,MAAI,uBAAuB;AAC3B,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AACA,QAAM,QAAQ,eAAe,OAAO,eAAe;AACnD,MAAI,uBAAuB,OAAO;AAChC;AAAA,EACF;AACA,uBAAqB;AACrB,MAAI,iBAAiB,MAAM;AACzB,mBAAe,SAAS,cAAc,OAAO;AAC7C,UAAMC,SAAQ,SAAS;AACvB,QAAIA,QAAO;AACT,mBAAa,aAAa,SAASA,MAAK;AAAA,IAC1C;AACA,aAAS,KAAK,YAAY,YAAY;AAAA,EACxC;AACA,MAAI,iBAAiB,GAAG;AACtB,QAAI;AACJ,KAAC,sBAAsB,aAAa,WAAW,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,WAAW,aAAa;AAAA,EAC/I;AACA,mBAAiB,yBAAyB,uBAAuB,aAAa,WAAW,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,WAAW,aAAa,KAAK,eAAe,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AACzR;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,SAAS;AACxB;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,MAAM,KAAK,WAAW,SAAS;AACxC;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,KAAK,WAAW,OAAO;AACtC;AAEA,SAAS,0BAA0B,OAAO;AACxC,MAAI,eAAe,KAAK,GAAG;AACzB,QAAI,MAAM,WAAW;AACnB,aAAO;AAAA,QACL,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,MACX;AAAA,IACF;AAAA,EACF,WAAW,aAAa,KAAK,GAAG;AAC9B,WAAO;AAAA,MACL,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,IACX;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,eAAe;AACtB,MAAI,OAAO,eAAe,YAAY;AACpC,WAAO,WAAW,kBAAkB,EAAE,UAAU,WAAW;AAAA,EAC7D;AACF;AAEA,SAAS,WAAW,SAAS,SAAS,QAAQ;AAC5C,MAAI,QAAQ;AACV,WAAO,QAAQ,IAAI,QAAQ,IAAI,QAAQ,SAAS,QAAQ,IAAI,QAAQ,QAAQ,QAAQ,KAAK,QAAQ,IAAI,QAAQ,IAAI,QAAQ,UAAU,QAAQ,IAAI,QAAQ,SAAS,QAAQ;AAAA,EAC1K,OAAO;AACL,WAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,SAAS,QAAQ,IAAI,QAAQ,SAAS,QAAQ,KAAK,QAAQ,KAAK,QAAQ,IAAI,QAAQ,UAAU,QAAQ,IAAI,QAAQ,UAAU,QAAQ;AAAA,EAC9K;AACF;AAUA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,MAAM,EAAG,OAAM,IAAI,MAAM,iCAAiC;AAC9D,QAAM,YAAY;AAAA,IAChB,GAAG,cAAc,CAAC;AAAA,IAClB,GAAG,cAAc,CAAC;AAAA,EACpB;AACA,MAAI;AAGJ,SAAO,UAAU,EAAE,GAAG,EAAE,MAAM,UAAU,EAAE,GAAG,EAAE,GAAG;AAChD,QAAI,UAAU,EAAE,IAAI;AACpB,QAAI,UAAU,EAAE,IAAI;AACpB,sBAAkB;AAAA,EACpB;AACA,SAAO,iBAAiB,2EAA2E;AACnG,QAAM,YAAY;AAAA,IAChB,GAAG,YAAY,sBAAsB,UAAU,CAAC,CAAC;AAAA,IACjD,GAAG,YAAY,sBAAsB,UAAU,CAAC,CAAC;AAAA,EACnD;AACA,MAAI,UAAU,MAAM,UAAU,GAAG;AAC/B,UAAM,WAAW,gBAAgB;AACjC,UAAM,qBAAqB;AAAA,MACzB,GAAG,UAAU,EAAE,GAAG,EAAE;AAAA,MACpB,GAAG,UAAU,EAAE,GAAG,EAAE;AAAA,IACtB;AACA,QAAI,IAAI,SAAS;AACjB,WAAO,KAAK;AACV,YAAM,QAAQ,SAAS,CAAC;AACxB,UAAI,UAAU,mBAAmB,EAAG,QAAO;AAC3C,UAAI,UAAU,mBAAmB,EAAG,QAAO;AAAA,IAC7C;AAAA,EACF;AACA,SAAO,KAAK,KAAK,UAAU,IAAI,UAAU,CAAC;AAC5C;AACA,IAAM,QAAQ;AAGd,SAAS,aAAa,MAAM;AAC1B,MAAI;AAEJ,QAAM,UAAU,kBAAkB,cAAc,WAAW,IAAI,OAAO,QAAQ,gBAAgB,SAAS,cAAc,IAAI,EAAE;AAC3H,SAAO,YAAY,UAAU,YAAY;AAC3C;AAGA,SAAS,yBAAyB,MAAM;AACtC,QAAM,QAAQ,iBAAiB,IAAI;AAGnC,MAAI,MAAM,aAAa,QAAS,QAAO;AAMvC,MAAI,MAAM,WAAW,WAAW,MAAM,aAAa,YAAY,aAAa,IAAI,GAAI,QAAO;AAC3F,MAAI,CAAC,MAAM,UAAU,EAAG,QAAO;AAC/B,MAAI,eAAe,SAAS,MAAM,cAAc,OAAQ,QAAO;AAC/D,MAAI,qBAAqB,SAAS,MAAM,oBAAoB,OAAQ,QAAO;AAC3E,MAAI,kBAAkB,SAAS,MAAM,iBAAiB,SAAU,QAAO;AACvE,MAAI,YAAY,SAAS,MAAM,WAAW,OAAQ,QAAO;AACzD,MAAI,kBAAkB,SAAS,MAAM,iBAAiB,OAAQ,QAAO;AACrE,MAAI,eAAe,SAAS,MAAM,cAAc,UAAW,QAAO;AAClE,MAAI,MAAM,KAAK,MAAM,UAAU,EAAG,QAAO;AAEzC,MAAI,MAAM,4BAA4B,QAAS,QAAO;AACtD,SAAO;AACT;AAGA,SAAS,sBAAsB,OAAO;AACpC,MAAI,IAAI,MAAM;AACd,SAAO,KAAK;AACV,UAAM,OAAO,MAAM,CAAC;AACpB,WAAO,MAAM,cAAc;AAC3B,QAAI,yBAAyB,IAAI,EAAG,QAAO;AAAA,EAC7C;AACA,SAAO;AACT;AAGA,SAAS,YAAY,MAAM;AACzB,SAAO,QAAQ,OAAO,iBAAiB,IAAI,EAAE,MAAM,KAAK;AAC1D;AAGA,SAAS,cAAc,MAAM;AAC3B,QAAM,YAAY,CAAC;AACnB,SAAO,MAAM;AACX,cAAU,KAAK,IAAI;AAEnB,WAAO,WAAW,IAAI;AAAA,EACxB;AACA,SAAO;AACT;AAGA,SAAS,WAAW,MAAM;AACxB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,cAAc,sBAAsB,YAAY;AAClD,WAAO,WAAW;AAAA,EACpB;AACA,SAAO;AACT;AAEA,IAAM,0BAA0B;AAChC,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,kBAAkB,aAAa,MAAM;AAC3C,IAAI,sBAAsB,CAAC;AAC3B,IAAI,gBAAgB;AACpB,IAAI,sBAAsB,oBAAI,IAAI;AAClC,IAAI,uBAAuB,oBAAI,IAAI;AACnC,IAAM,2BAA2B,oBAAI,IAAI;AACzC,SAAS,qBAAqB,gBAAgB,SAAS,WAAW,gBAAgB,uBAAuB;AACvG,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,SAAS,wBAAwB,oBAAoB,IAAI,aAAa,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AACtJ,sBAAoB,IAAI,eAAe,QAAQ,CAAC;AAChD,2BAAyB,IAAI,IAAI;AACjC,kBAAgB;AAChB,SAAO,SAAS,yBAAyB;AACvC,QAAI;AACJ,yBAAqB,OAAO,cAAc;AAC1C,6BAAyB,OAAO,IAAI;AACpC,UAAMC,UAAS,yBAAyB,oBAAoB,IAAI,aAAa,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB;AACzJ,wBAAoB,IAAI,eAAeA,SAAQ,CAAC;AAChD,oBAAgB;AAChB,QAAIA,WAAU,GAAG;AACf,0BAAoB,OAAO,aAAa;AAAA,IAC1C;AAIA,QAAI,oBAAoB,SAAS,IAAI,GAAG;AACtC,YAAM,QAAQ,oBAAoB,QAAQ,IAAI;AAC9C,UAAI,SAAS,GAAG;AACd,4BAAoB,OAAO,OAAO,CAAC;AAAA,MACrC;AACA,mBAAa;AAIb,4BAAsB,MAAM,MAAM,IAAI;AAAA,IACxC;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,0BAA0B,KAAK;AACnC,kBAAgB;AAChB,iCAA+B;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,kBAAgB;AAChB,MAAI,oBAAoB,SAAS,GAAG;AAClC,8BAA0B,QAAQ,KAAK;AACvC,UAAM,eAAe;AACrB,QAAI,CAAC,qBAAqB,MAAM,GAAG;AACjC,YAAM,yBAAyB;AAAA,IACjC;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,0BAA0B,KAAK;AAInC,MAAI,iBAAiB,MAAM,YAAY,GAAG;AACxC,oBAAgB;AAChB,8BAA0B,MAAM,KAAK;AAAA,EACvC;AACA,MAAI,CAAC,eAAe;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAKJ,mCAA+B;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,4BAA0B,QAAQ,KAAK;AAGvC,eAAa;AACb,MAAI,oBAAoB,SAAS,GAAG;AAClC,UAAM,eAAe;AAAA,EACvB;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,0BAA0B,KAAK;AACnC,uBAAqB,MAAM;AAC3B,kBAAgB;AAChB,MAAI,oBAAoB,SAAS,GAAG;AAClC,UAAM,eAAe;AACrB,QAAI,CAAC,qBAAqB,MAAM,GAAG;AACjC,YAAM,yBAAyB;AAAA,IACjC;AAAA,EACF;AACA,4BAA0B,MAAM,KAAK;AACrC,iCAA+B;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,eAAa;AACb,kBAAgB;AAClB;AACA,SAAS,qBAAqB,SAAS;AACrC,MAAI,iBAAiB;AACrB,SAAO,gBAAgB;AACrB,QAAI,eAAe,aAAa,gBAAgB,YAAY,GAAG;AAC7D,aAAO;AAAA,IACT;AACA,qBAAiB,eAAe;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,+BAA+B;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,sBAAoB,OAAO,CAAC;AAC5B,MAAI,gBAAgB;AACpB,MAAI,kBAAkB,eAAe,kBAAkB,YAAY;AACjE,oBAAgB;AAAA,EAClB;AACA,2BAAyB,QAAQ,UAAQ;AACvC,UAAM;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,IACF,IAAI;AACJ,UAAM,iBAAiB,kBAAkB,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,kBAAkB,eAAe,SAAS,eAAe;AACxE,UAAM,kBAAkB,KAAK,OAAO,UAAU,KAAK,QAAQ,UAAU,KAAK,MAAM,UAAU,KAAK,SAAS;AACxG,QAAI,iBAAiB;AAOnB,UAAI,kBAAkB,QAAQ,SAAS,SAAS,aAAa,KAAK,sBAAsB,iBAAiB,CAAC,kBAAkB,SAAS,aAAa,KAAK,CAAC,cAAc,SAAS,iBAAiB;AAAA;AAAA;AAAA,MAIhM,QAAQ,eAAe,iBAAiB,IAAI,GAAG;AAO7C,YAAI,iBAAiB;AACrB,YAAI,eAAe;AACnB,eAAO,gBAAgB;AACrB,cAAI,eAAe,SAAS,iBAAiB,GAAG;AAC9C;AAAA,UACF,WAAW,WAAW,eAAe,sBAAsB,GAAG,gBAAgB,IAAI,GAAG;AACnF,2BAAe;AACf;AAAA,UACF;AACA,2BAAiB,eAAe;AAAA,QAClC;AACA,YAAI,cAAc;AAChB;AAAA,QACF;AAAA,MACF;AACA,0BAAoB,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF,CAAC;AACH;AACA,SAAS,2BAA2B,gBAAgB,MAAM;AACxD,uBAAqB,IAAI,gBAAgB,IAAI;AAC/C;AACA,SAAS,eAAe;AACtB,MAAI,uBAAuB;AAC3B,MAAI,qBAAqB;AACzB,sBAAoB,QAAQ,UAAQ;AAClC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,cAAc,cAAc;AAC9B,6BAAuB;AAAA,IACzB,OAAO;AACL,2BAAqB;AAAA,IACvB;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB;AACtB,uBAAqB,QAAQ,UAAQ;AACnC,uBAAmB;AAAA,EACrB,CAAC;AACD,MAAI,wBAAwB,oBAAoB;AAC9C,yBAAqB,gBAAgB,eAAe;AAAA,EACtD,WAAW,sBAAsB;AAC/B,yBAAqB,cAAc,eAAe;AAAA,EACpD,WAAW,oBAAoB;AAC7B,yBAAqB,YAAY,eAAe;AAAA,EAClD,OAAO;AACL,2BAAuB;AAAA,EACzB;AACF;AACA,IAAI;AACJ,SAAS,kBAAkB;AACzB,MAAI;AACJ,GAAC,wBAAwB,8BAA8B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,MAAM;AACvI,6BAA2B,IAAI,gBAAgB;AAC/C,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,QAAQ,yBAAyB;AAAA,EACnC;AACA,MAAI,CAAC,yBAAyB,MAAM;AAClC;AAAA,EACF;AACA,MAAI,eAAe;AACjB,QAAI,oBAAoB,SAAS,GAAG;AAClC,0BAAoB,QAAQ,CAAC,OAAO,kBAAkB;AACpD,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,QAAQ,GAAG;AACb,eAAK,iBAAiB,eAAe,iBAAiB,OAAO;AAC7D,eAAK,iBAAiB,gBAAgB,mBAAmB,OAAO;AAChE,eAAK,iBAAiB,eAAe,mBAAmB,OAAO;AAAA,QACjE;AAAA,MACF,CAAC;AAAA,IACH;AACA,wBAAoB,QAAQ,CAAC,GAAG,kBAAkB;AAChD,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,WAAK,iBAAiB,aAAa,iBAAiB,OAAO;AAC3D,WAAK,iBAAiB,iBAAiB,iBAAiB,OAAO;AAAA,IACjE,CAAC;AAAA,EACH,OAAO;AACL,wBAAoB,QAAQ,CAAC,OAAO,kBAAkB;AACpD,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ,GAAG;AACb,aAAK,iBAAiB,eAAe,mBAAmB,OAAO;AAC/D,aAAK,iBAAiB,eAAe,mBAAmB,OAAO;AAAA,MACjE;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,0BAA0B,QAAQ,OAAO;AAChD,2BAAyB,QAAQ,UAAQ;AACvC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,oBAAoB,SAAS,IAAI;AAClD,0BAAsB,QAAQ,UAAU,KAAK;AAAA,EAC/C,CAAC;AACH;AAEA,SAAS,iBAAiB;AACxB,QAAM,CAAC,GAAG,QAAQ,QAAI,uBAAS,CAAC;AAChC,aAAO,0BAAY,MAAM,SAAS,eAAa,YAAY,CAAC,GAAG,CAAC,CAAC;AACnE;AAEA,SAAS,OAAO,mBAAmB,SAAS;AAC1C,MAAI,CAAC,mBAAmB;AACtB,YAAQ,MAAM,OAAO;AACrB,UAAM,MAAM,OAAO;AAAA,EACrB;AACF;AAEA,SAAS,oBAAoB,QAAQ,UAAU,iBAAiB,WAAW;AACzE,MAAI,OAAO,QAAQ,cAAc,MAAM,SAAS,QAAQ,cAAc,GAAG;AACvE,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,WAAW,IAAI;AAAA,EACjC;AACF;AACA,SAAS,oBAAoB,QAAQ,UAAU,iBAAiB,WAAW;AACzE,SAAO,oBAAoB,QAAQ,UAAU,cAAc,MAAM;AACnE;AAEA,SAAS,kBAAkB,QAAQ,UAAU,gBAAgB;AAC3D,SAAO,oBAAoB,QAAQ,UAAU,cAAc,MAAM;AACnE;AAEA,SAAS,kBAAkB,QAAQ,UAAU,gBAAgB;AAC3D,MAAI,OAAO,WAAW,SAAS,QAAQ;AACrC,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAClD,UAAM,aAAa,OAAO,KAAK;AAC/B,UAAM,eAAe,SAAS,KAAK;AACnC,QAAI,CAAC,kBAAkB,YAAY,cAAc,cAAc,GAAG;AAChE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,YAAY;AAAA,EACnB,kBAAkB;AAAA,EAClB;AAAA,EACA;AACF,GAAG;AACD,QAAM,mBAAmB,sBAAsB,UAAU;AACzD,SAAO,oBAAoB,MAAM,yCAAyC,UAAU,EAAE;AACtF,MAAI;AAAA,IACF,gBAAgB;AAAA,IAChB;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,IAAI;AACJ,MAAI,oBAAoB,MAAM,OAAO,IAAI,GAAG;AAC1C,QAAI,aAAa;AAEf,YAAM,gBAAgB,gBAAgB,WAAW;AACjD,UAAI,oBAAoB,MAAM,YAAY,IAAI,GAAG;AAC/C,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,KAAK,IAAI,SAAS,IAAI;AAC7B,SAAO,WAAW,KAAK,QAAQ,SAAS,CAAC;AACzC,SAAO;AACT;AAGA,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,kBAAkB,OAAO,CAAC,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,GAAG,aAAa;AACpC,QAAM,CAAC,iBAAiB,gBAAgB,IAAI;AAC5C,SAAO,mBAAmB,MAAM,2BAA2B;AAC3D,SAAO,oBAAoB,MAAM,4BAA4B;AAC7D,MAAI,eAAe;AAmBnB;AAGE,QAAI,YAAY,YAAY;AAC1B;AAEE,cAAM,QAAQ,QAAQ,IAAI,mBAAmB;AAC7C,cAAM,mBAAmB,sBAAsB,KAAK;AACpD,eAAO,kBAAkB,yCAAyC,KAAK,EAAE;AACzE,cAAM;AAAA,UACJ,gBAAgB;AAAA,UAChB;AAAA,UACA,UAAU;AAAA,QACZ,IAAI;AAIJ,YAAI,aAAa;AACf,gBAAM,WAAW,cAAc,KAAK;AACpC,iBAAO,YAAY,MAAM,6CAA6C,KAAK,EAAE;AAC7E,cAAI,kBAAkB,UAAU,aAAa,GAAG;AAC9C,kBAAM,aAAa,UAAU;AAG7B,gBAAI,oBAAoB,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG;AACxD,sBAAQ,QAAQ,IAAI,IAAI,aAAa;AAAA,YAEvC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA;AAEE,cAAM,QAAQ,QAAQ,IAAI,kBAAkB;AAC5C,cAAM,mBAAmB,sBAAsB,KAAK;AACpD,eAAO,kBAAkB,wCAAwC,KAAK,EAAE;AACxE,cAAM;AAAA,UACJ,gBAAgB;AAAA,UAChB;AAAA,UACA,UAAU;AAAA,QACZ,IAAI;AAIJ,YAAI,aAAa;AACf,gBAAM,WAAW,cAAc,KAAK;AACpC,iBAAO,YAAY,MAAM,6CAA6C,KAAK,EAAE;AAC7E,cAAI,kBAAkB,UAAU,OAAO,GAAG;AACxC,kBAAM,aAAa,WAAW;AAG9B,gBAAI,oBAAoB,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG;AACxD,sBAAQ,QAAQ,IAAI,IAAI,aAAa;AAAA,YAEvC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EAEF;AAEA;AAOE,UAAM,YAAY,QAAQ,IAAI,IAAI;AAClC,QAAI,QAAQ,QAAQ,IAAI,mBAAmB;AAC3C,QAAI,oBAAoB;AAGxB,WAAO,MAAM;AACX,YAAM,WAAW,cAAc,KAAK;AACpC,aAAO,YAAY,MAAM,6CAA6C,KAAK,EAAE;AAC7E,YAAM,cAAc,YAAY;AAAA,QAC9B,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AACD,YAAMC,SAAQ,cAAc;AAG5B,2BAAqBA;AACrB,eAAS;AACT,UAAI,QAAQ,KAAK,SAAS,sBAAsB,QAAQ;AACtD;AAAA,MACF;AAAA,IACF;AAGA,UAAM,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,iBAAiB,CAAC;AACzE,YAAQ,QAAQ,IAAI,IAAI,cAAc;AAAA,EAGxC;AAEA;AAGE,UAAM,aAAa,QAAQ,IAAI,kBAAkB;AACjD,QAAI,QAAQ;AACZ,WAAO,SAAS,KAAK,QAAQ,sBAAsB,QAAQ;AACzD,YAAM,iBAAiB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,YAAY;AAC9D,YAAM,WAAW,cAAc,KAAK;AACpC,aAAO,YAAY,MAAM,6CAA6C,KAAK,EAAE;AAC7E,YAAM,aAAa,WAAW;AAC9B,YAAM,WAAW,YAAY;AAAA,QAC3B,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AACD,UAAI,CAAC,kBAAkB,UAAU,QAAQ,GAAG;AAC1C,wBAAgB,WAAW;AAC3B,mBAAW,KAAK,IAAI;AACpB,YAAI,aAAa,YAAY,CAAC,EAAE,cAAc,KAAK,IAAI,KAAK,EAAE,YAAY,CAAC,GAAG,QAAW;AAAA,UACvF,SAAS;AAAA,QACX,CAAC,KAAK,GAAG;AACP;AAAA,QACF;AAAA,MACF;AACA,UAAI,QAAQ,GAAG;AACb;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAOA,MAAI,kBAAkB,YAAY,UAAU,GAAG;AAI7C,WAAO;AAAA,EACT;AACA;AAEE,UAAM,aAAa,QAAQ,IAAI,mBAAmB;AAClD,UAAM,WAAW,cAAc,UAAU;AACzC,WAAO,YAAY,MAAM,6CAA6C,UAAU,EAAE;AAClF,UAAM,aAAa,WAAW;AAC9B,UAAM,WAAW,YAAY;AAAA,MAC3B,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAGD,eAAW,UAAU,IAAI;AAGzB,QAAI,CAAC,kBAAkB,UAAU,UAAU,GAAG;AAC5C,UAAI,iBAAiB,aAAa;AAClC,YAAMC,cAAa,QAAQ,IAAI,mBAAmB;AAClD,UAAI,QAAQA;AACZ,aAAO,SAAS,KAAK,QAAQ,sBAAsB,QAAQ;AACzD,cAAMC,YAAW,WAAW,KAAK;AACjC,eAAOA,aAAY,MAAM,6CAA6C,KAAK,EAAE;AAC7E,cAAMC,cAAaD,YAAW;AAC9B,cAAME,YAAW,YAAY;AAAA,UAC3B,kBAAkB;AAAA,UAClB,YAAY;AAAA,UACZ,MAAMD;AAAA,QACR,CAAC;AACD,YAAI,CAAC,kBAAkBD,WAAUE,SAAQ,GAAG;AAC1C,4BAAkBA,YAAWF;AAC7B,qBAAW,KAAK,IAAIE;AAAA,QACtB;AACA,YAAI,kBAAkB,gBAAgB,CAAC,GAAG;AACxC;AAAA,QACF;AACA,YAAI,QAAQ,GAAG;AACb;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAKA,QAAM,YAAY,WAAW,OAAO,CAAC,OAAO,SAAS,OAAO,OAAO,CAAC;AAKpE,MAAI,CAAC,kBAAkB,WAAW,GAAG,GAAG;AAItC,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AAEA,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,QAAM,aAAa,aAAa,CAAC;AACjC,SAAO,cAAc,MAAM,sBAAsB;AAGjD,cAAY,QAAQ,CAAC,WAAW,UAAU;AACxC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,IAAI;AACJ,QAAI,UAAU,YAAY;AACxB,uBAAiB;AACjB,uBAAiB;AAAA,IACnB,OAAO;AACL,sBAAgB;AAChB,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,QAAM,WAAW,KAAK,IAAI,gBAAgB,MAAM,YAAY;AAC5D,QAAM,WAAW,KAAK,IAAI,gBAAgB,MAAM,YAAY;AAC5D,QAAM,WAAW,OAAO,UAAU;AAClC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gCAAgC,SAAS,QAAQ,UAAU;AAClE,SAAO,MAAM,KAAK,MAAM,iBAAiB,IAAI,gBAAgB,cAAc,0BAA0B,OAAO,IAAI,CAAC;AACnH;AAEA,SAAS,4BAA4B,SAAS,IAAI,QAAQ,UAAU;AAClE,QAAM,UAAU,gCAAgC,SAAS,KAAK;AAC9D,QAAM,QAAQ,QAAQ,UAAU,YAAU,OAAO,aAAa,gBAAgB,cAAc,MAAM,EAAE;AACpG,SAAO,UAAU,QAAQ,UAAU,SAAS,QAAQ;AACtD;AAEA,SAAS,sBAAsB,SAAS,cAAc,mBAAmB;AACvE,QAAM,QAAQ,4BAA4B,SAAS,cAAc,iBAAiB;AAClF,SAAO,SAAS,OAAO,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE;AACrD;AAEA,SAAS,cAAc,QAAQ;AAC7B,MAAI,kBAAkB,aAAa;AACjC,WAAO;AAAA,EACT;AAGA,SAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,aAAa,UAAU,kBAAkB;AACnG;AAEA,SAAS,qBAAqB,IAAI,cAAc,UAAU;AAExD,MAAI,cAAc,WAAW,KAAK,YAAY,QAAQ,gBAAgB,IAAI;AACxE,WAAO;AAAA,EACT;AAGA,QAAM,UAAU,YAAY,cAAc,2CAA2C,EAAE,IAAI;AAC3F,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,uBAAuB,IAAI,QAAQ,UAAU;AACpD,QAAM,UAAU,MAAM,cAAc,IAAI,gBAAgB,cAAc,KAAK,EAAE,IAAI;AACjF,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,wBAAwB,SAAS,UAAU,aAAa,QAAQ,UAAU;AACjF,MAAI,uBAAuB,oBAAoB,iBAAiB;AAChE,QAAM,SAAS,uBAAuB,UAAU,KAAK;AACrD,QAAM,UAAU,gCAAgC,SAAS,KAAK;AAC9D,QAAM,QAAQ,SAAS,QAAQ,QAAQ,MAAM,IAAI;AACjD,QAAM,YAAY,yBAAyB,qBAAqB,YAAY,KAAK,OAAO,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,QAAQ,QAAQ,0BAA0B,SAAS,wBAAwB;AACvO,QAAM,WAAW,mBAAmB,eAAe,YAAY,QAAQ,CAAC,OAAO,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,QAAQ,oBAAoB,SAAS,kBAAkB;AACtM,SAAO,CAAC,UAAU,OAAO;AAC3B;AAIA,SAAS,oCAAoC;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,qBAAiB,qBAAO;AAAA,IAC5B,iCAAiC;AAAA,EACnC,CAAC;AACD,4BAA0B,MAAM;AAC9B,QAAI,CAAC,mBAAmB;AACtB;AAAA,IACF;AACA,UAAM,uBAAuB,gCAAgC,SAAS,iBAAiB;AACvF,aAAS,QAAQ,GAAG,QAAQ,eAAe,SAAS,GAAG,SAAS;AAC9D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,oBAAoB;AAAA,QACtB;AAAA,QACA,aAAa;AAAA,QACb,cAAc,CAAC,OAAO,QAAQ,CAAC;AAAA,MACjC,CAAC;AACD,YAAM,sBAAsB,qBAAqB,KAAK;AACtD,UAAI,uBAAuB,MAAM;AAC/B;AACE,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,eAAe;AACnB,cAAI,CAAC,iCAAiC;AACpC,2BAAe,QAAQ,kCAAkC;AACzD,oBAAQ,KAAK,kDAAkD,OAAO,GAAG;AAAA,UAC3E;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,YAAY,eAAe,KAAK;AACtC,eAAO,WAAW,kCAAkC,KAAK,GAAG;AAC5D,4BAAoB,aAAa,iBAAiB,UAAU,EAAE;AAC9D,4BAAoB,aAAa,iBAAiB,KAAK,KAAK,MAAM,QAAQ,CAAC;AAC3E,4BAAoB,aAAa,iBAAiB,KAAK,KAAK,MAAM,QAAQ,CAAC;AAC3E,4BAAoB,aAAa,iBAAiB,YAAY,OAAO,KAAK,KAAK,MAAM,QAAQ,IAAI,EAAE;AAAA,MACrG;AAAA,IACF;AACA,WAAO,MAAM;AACX,2BAAqB,QAAQ,CAAC,qBAAqB,UAAU;AAC3D,4BAAoB,gBAAgB,eAAe;AACnD,4BAAoB,gBAAgB,eAAe;AACnD,4BAAoB,gBAAgB,eAAe;AACnD,4BAAoB,gBAAgB,eAAe;AAAA,MACrD,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,SAAS,QAAQ,gBAAgB,iBAAiB,CAAC;AACvD,8BAAU,MAAM;AACd,QAAI,CAAC,mBAAmB;AACtB;AAAA,IACF;AACA,UAAM,cAAc,eAAe;AACnC,WAAO,aAAa,wBAAwB;AAC5C,UAAM;AAAA,MACJ,gBAAAC;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,qBAAqB,SAAS,iBAAiB;AACpE,WAAO,gBAAgB,MAAM,0BAA0B,OAAO,GAAG;AACjE,UAAM,UAAU,gCAAgC,SAAS,iBAAiB;AAC1E,WAAO,SAAS,yCAAyC,OAAO,GAAG;AACnE,UAAM,mBAAmB,QAAQ,IAAI,YAAU;AAC7C,YAAM,WAAW,OAAO,aAAa,gBAAgB,cAAc;AACnE,aAAO,UAAU,kDAAkD;AACnE,YAAM,CAAC,UAAU,OAAO,IAAI,wBAAwB,SAAS,UAAUA,iBAAgB,iBAAiB;AACxG,UAAI,YAAY,QAAQ,WAAW,MAAM;AACvC,eAAO,MAAM;AAAA,QAAC;AAAA,MAChB;AACA,YAAM,YAAY,WAAS;AACzB,YAAI,MAAM,kBAAkB;AAC1B;AAAA,QACF;AACA,gBAAQ,MAAM,KAAK;AAAA,UACjB,KAAK,SACH;AACE,kBAAM,eAAe;AACrB,kBAAM,QAAQA,gBAAe,UAAU,eAAa,UAAU,OAAO,QAAQ;AAC7E,gBAAI,SAAS,GAAG;AACd,oBAAM,YAAYA,gBAAe,KAAK;AACtC,qBAAO,WAAW,iCAAiC,KAAK,EAAE;AAC1D,oBAAM,OAAO,OAAO,KAAK;AACzB,oBAAM;AAAA,gBACJ,gBAAgB;AAAA,gBAChB;AAAA,gBACA,UAAU;AAAA,cACZ,IAAI,UAAU;AACd,kBAAI,QAAQ,QAAQ,aAAa;AAC/B,sBAAM,aAAa,oBAAoB;AAAA,kBACrC,OAAO,kBAAkB,MAAM,aAAa,IAAI,UAAU,gBAAgB,gBAAgB;AAAA,kBAC1F,eAAe;AAAA,kBACf,kBAAkBA,gBAAe,IAAI,CAAAC,eAAaA,WAAU,WAAW;AAAA,kBACvE,cAAc,sBAAsB,SAAS,UAAU,iBAAiB;AAAA,kBACxE,YAAY;AAAA,kBACZ,SAAS;AAAA,gBACX,CAAC;AACD,oBAAI,WAAW,YAAY;AACzB,4BAAU,UAAU;AAAA,gBACtB;AAAA,cACF;AAAA,YACF;AACA;AAAA,UACF;AAAA,QACJ;AAAA,MACF;AACA,aAAO,iBAAiB,WAAW,SAAS;AAC5C,aAAO,MAAM;AACX,eAAO,oBAAoB,WAAW,SAAS;AAAA,MACjD;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,uBAAiB,QAAQ,qBAAmB,gBAAgB,CAAC;AAAA,IAC/D;AAAA,EACF,GAAG,CAAC,mBAAmB,oBAAoB,gBAAgB,SAAS,QAAQ,gBAAgB,SAAS,CAAC;AACxG;AAEA,SAAS,SAAS,QAAQ,QAAQ;AAChC,MAAI,OAAO,WAAW,OAAO,QAAQ;AACnC,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAClD,QAAI,OAAO,KAAK,MAAM,OAAO,KAAK,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,6BAA6B,WAAW,OAAO;AACtD,QAAM,eAAe,cAAc;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,0BAA0B,KAAK;AACnC,SAAO,eAAe,IAAI;AAC5B;AAEA,SAAS,8BAA8B,OAAO,cAAc,WAAW,kBAAkB,mBAAmB;AAC1G,QAAM,eAAe,cAAc;AACnC,QAAM,gBAAgB,uBAAuB,cAAc,iBAAiB;AAC5E,SAAO,eAAe,0CAA0C,YAAY,GAAG;AAC/E,QAAM,UAAU,cAAc,aAAa,gBAAgB,OAAO;AAClE,SAAO,SAAS,iDAAiD;AACjE,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM,iBAAiB,6BAA6B,WAAW,KAAK;AACpE,QAAM,eAAe,qBAAqB,SAAS,iBAAiB;AACpE,SAAO,cAAc,kCAAkC,OAAO,GAAG;AACjE,QAAM,YAAY,aAAa,sBAAsB;AACrD,QAAM,oBAAoB,eAAe,UAAU,QAAQ,UAAU;AACrE,QAAM,eAAe,iBAAiB;AACtC,QAAM,mBAAmB,eAAe,oBAAoB;AAC5D,SAAO;AACT;AAGA,SAAS,yBAAyB,OAAO,cAAc,WAAW,kBAAkB,kBAAkB,mBAAmB;AACvH,MAAI,UAAU,KAAK,GAAG;AACpB,UAAM,eAAe,cAAc;AACnC,QAAI,QAAQ;AACZ,QAAI,MAAM,UAAU;AAClB,cAAQ;AAAA,IACV,WAAW,oBAAoB,MAAM;AACnC,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ;AAAA,IACV;AACA,QAAI,WAAW;AACf,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK;AACH,mBAAW,eAAe,IAAI;AAC9B;AAAA,MACF,KAAK;AACH,mBAAW,eAAe,CAAC,QAAQ;AACnC;AAAA,MACF,KAAK;AACH,mBAAW,eAAe,QAAQ;AAClC;AAAA,MACF,KAAK;AACH,mBAAW,eAAe,IAAI,CAAC;AAC/B;AAAA,MACF,KAAK;AACH,mBAAW;AACX;AAAA,MACF,KAAK;AACH,mBAAW;AACX;AAAA,IACJ;AACA,WAAO;AAAA,EACT,OAAO;AACL,QAAI,oBAAoB,MAAM;AAC5B,aAAO;AAAA,IACT;AACA,WAAO,8BAA8B,OAAO,cAAc,WAAW,kBAAkB,iBAAiB;AAAA,EAC1G;AACF;AAEA,SAAS,6BAA6B;AAAA,EACpC;AACF,GAAG;AACD,QAAM,SAAS,MAAM,eAAe,MAAM;AAC1C,QAAM,wBAAwB,eAAe,IAAI,eAAa,UAAU,WAAW;AACnF,MAAI,qBAAqB;AACzB,MAAI,gBAAgB;AAGpB,WAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC1D,UAAM,mBAAmB,sBAAsB,KAAK;AACpD,WAAO,kBAAkB,yCAAyC,KAAK,EAAE;AACzE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,eAAe,MAAM;AACvB;AACA,aAAO,KAAK,IAAI;AAChB,uBAAiB;AAAA,IACnB;AAAA,EACF;AAGA,WAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC1D,UAAM,mBAAmB,sBAAsB,KAAK;AACpD,WAAO,kBAAkB,yCAAyC,KAAK,EAAE;AACzE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,eAAe,MAAM;AACvB;AAAA,IACF;AACA,UAAM,qBAAqB,eAAe,SAAS;AACnD,UAAM,OAAO,gBAAgB;AAC7B;AACA,WAAO,KAAK,IAAI;AAChB,qBAAiB;AAAA,EACnB;AACA,SAAO;AACT;AAGA,SAAS,mBAAmB,aAAa,QAAQ,8BAA8B;AAC7E,SAAO,QAAQ,CAAC,MAAM,UAAU;AAC9B,UAAM,YAAY,YAAY,KAAK;AACnC,WAAO,WAAW,kCAAkC,KAAK,EAAE;AAC3D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,IAAI;AACJ,UAAM;AAAA,MACJ,gBAAgB;AAAA,MAChB;AAAA,IACF,IAAI;AACJ,UAAM,mBAAmB,6BAA6B,OAAO;AAC7D,QAAI,oBAAoB,QAAQ,SAAS,kBAAkB;AACzD,mCAA6B,OAAO,IAAI;AACxC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,UAAU;AACZ,iBAAS,MAAM,gBAAgB;AAAA,MACjC;AACA,UAAI,gBAAgB,cAAc,WAAW;AAC3C,YAAI,aAAa,oBAAoB,QAAQ,oBAAoB,kBAAkB,aAAa,MAAM,CAAC,oBAAoB,MAAM,aAAa,GAAG;AAC/I,mBAAS;AAAA,QACX;AACA,YAAI,eAAe,oBAAoB,QAAQ,CAAC,oBAAoB,kBAAkB,aAAa,MAAM,oBAAoB,MAAM,aAAa,GAAG;AACjJ,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe,GAAG,GAAG;AAC5B,MAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,WAAO;AAAA,EACT,OAAO;AACL,aAAS,QAAQ,GAAG,QAAQ,EAAE,QAAQ,SAAS;AAC7C,UAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,yBAAyB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AACd,GAAG;AACD,QAAM,OAAO,OAAO,UAAU;AAC9B,MAAI;AACJ,MAAI,QAAQ,MAAM;AAGhB,eAAW,eAAe,SAAY,YAAY,YAAY,SAAS,IAAI;AAAA,EAC7E,WAAW,UAAU,WAAW,GAAG;AAEjC,eAAW;AAAA,EACb,OAAO;AACL,eAAW,KAAK,YAAY,SAAS;AAAA,EACvC;AACA,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,IACA,YAAY;AAAA;AAAA,IAEZ,UAAU;AAAA;AAAA;AAAA,IAGV,eAAe,cAAc,OAAO,SAAS;AAAA,EAC/C;AACF;AAEA,SAAS,SAAS,UAAU,aAAa,IAAI;AAC3C,MAAI,YAAY;AAChB,MAAI,WAAW,IAAI,SAAS;AAC1B,QAAI,cAAc,MAAM;AACtB,mBAAa,SAAS;AAAA,IACxB;AACA,gBAAY,WAAW,MAAM;AAC3B,eAAS,GAAG,IAAI;AAAA,IAClB,GAAG,UAAU;AAAA,EACf;AACA,SAAO;AACT;AAMA,SAAS,yBAAyB,eAAe;AAC/C,MAAI;AACF,QAAI,OAAO,iBAAiB,aAAa;AAEvC,oBAAc,UAAU,UAAQ;AAC9B,eAAO,aAAa,QAAQ,IAAI;AAAA,MAClC;AACA,oBAAc,UAAU,CAAC,MAAM,UAAU;AACvC,qBAAa,QAAQ,MAAM,KAAK;AAAA,MAClC;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,KAAK;AACnB,kBAAc,UAAU,MAAM;AAC9B,kBAAc,UAAU,MAAM;AAAA,IAAC;AAAA,EACjC;AACF;AAEA,SAAS,iBAAiB,YAAY;AACpC,SAAO,0BAA0B,UAAU;AAC7C;AAMA,SAAS,YAAY,QAAQ;AAC3B,SAAO,OAAO,IAAI,WAAS;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,eAAe;AACjB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,QAAQ,GAAG,KAAK,IAAI,KAAK,UAAU,WAAW,CAAC,KAAK,KAAK,UAAU,WAAW;AAAA,IACvF;AAAA,EACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,KAAK,GAAG;AAChD;AACA,SAAS,8BAA8B,YAAY,SAAS;AAC1D,MAAI;AACF,UAAM,gBAAgB,iBAAiB,UAAU;AACjD,UAAM,aAAa,QAAQ,QAAQ,aAAa;AAChD,QAAI,YAAY;AACd,YAAM,SAAS,KAAK,MAAM,UAAU;AACpC,UAAI,OAAO,WAAW,YAAY,UAAU,MAAM;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AAAA,EAAC;AACjB,SAAO;AACT;AACA,SAAS,oBAAoB,YAAY,QAAQ,SAAS;AACxD,MAAI,uBAAuB;AAC3B,QAAM,SAAS,wBAAwB,8BAA8B,YAAY,OAAO,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB,CAAC;AACnK,QAAM,WAAW,YAAY,MAAM;AACnC,UAAQ,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,oBAAoB,SAAS,kBAAkB;AACxG;AACA,SAAS,oBAAoB,YAAY,QAAQ,0BAA0B,OAAO,SAAS;AACzF,MAAI;AACJ,QAAM,gBAAgB,iBAAiB,UAAU;AACjD,QAAM,WAAW,YAAY,MAAM;AACnC,QAAM,SAAS,yBAAyB,8BAA8B,YAAY,OAAO,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB,CAAC;AACtK,QAAM,QAAQ,IAAI;AAAA,IAChB,eAAe,OAAO,YAAY,yBAAyB,QAAQ,CAAC;AAAA,IACpE,QAAQ;AAAA,EACV;AACA,MAAI;AACF,YAAQ,QAAQ,eAAe,KAAK,UAAU,KAAK,CAAC;AAAA,EACtD,SAAS,OAAO;AACd,YAAQ,MAAM,KAAK;AAAA,EACrB;AACF;AAEA,SAAS,yBAAyB;AAAA,EAChC,kBAAkB;AAAA,EAClB;AAAA,EACA;AACF,GAAG;AACD;AACE,UAAM,WAAW,CAAC;AAClB,UAAM,mBAAmB,sBAAsB,UAAU;AACzD,WAAO,kBAAkB,wCAAwC,UAAU,EAAE;AAC7E,UAAM;AAAA,MACJ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd;AAAA,MACA,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,IAAI;AACJ,QAAI,UAAU,SAAS;AACrB,eAAS,KAAK,aAAa,OAAO,2CAA2C,OAAO,IAAI;AAAA,IAC1F;AACA,QAAI,eAAe,MAAM;AACvB,UAAI,cAAc,GAAG;AACnB,iBAAS,KAAK,wCAAwC;AAAA,MACxD,WAAW,cAAc,YAAY,CAAC,eAAe,gBAAgB,gBAAgB;AACnF,iBAAS,KAAK,+CAA+C;AAAA,MAC/D;AACA,UAAI,cAAc,KAAK;AACrB,iBAAS,KAAK,6CAA6C;AAAA,MAC7D,WAAW,cAAc,SAAS;AAChC,iBAAS,KAAK,kDAAkD;AAAA,MAClE;AAAA,IACF;AACA,QAAI,gBAAgB,SAAS;AAC3B,eAAS,KAAK,oDAAoD;AAAA,IACpE;AACA,QAAI,SAAS,SAAS,GAAG;AACvB,YAAM,OAAO,WAAW,OAAO,UAAU,OAAO,MAAM;AACtD,cAAQ,KAAK,GAAG,IAAI;AAAA;AAAA,EAAqC,SAAS,KAAK,IAAI,CAAC,EAAE;AAC9E,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,yBAAyB;AAAA,EAChC,QAAQ;AAAA,EACR;AACF,GAAG;AACD,QAAM,aAAa,CAAC,GAAG,UAAU;AACjC,QAAM,sBAAsB,WAAW,OAAO,CAAC,aAAa,YAAY,cAAc,SAAS,CAAC;AAGhG,MAAI,WAAW,WAAW,iBAAiB,QAAQ;AACjD,UAAM,MAAM,WAAW,iBAAiB,MAAM,kBAAkB,WAAW,IAAI,UAAQ,GAAG,IAAI,GAAG,EAAE,KAAK,IAAI,CAAC,EAAE;AAAA,EACjH,WAAW,CAAC,kBAAkB,qBAAqB,GAAG,KAAK,WAAW,SAAS,GAAG;AAGhF;AACE,cAAQ,KAAK,uCAAuC,WAAW,IAAI,UAAQ,GAAG,IAAI,GAAG,EAAE,KAAK,IAAI,CAAC,yCAAyC;AAAA,IAC5I;AACA,aAAS,QAAQ,GAAG,QAAQ,iBAAiB,QAAQ,SAAS;AAC5D,YAAM,aAAa,WAAW,KAAK;AACnC,aAAO,cAAc,MAAM,kCAAkC,KAAK,EAAE;AACpE,YAAM,WAAW,MAAM,sBAAsB;AAC7C,iBAAW,KAAK,IAAI;AAAA,IACtB;AAAA,EACF;AACA,MAAI,gBAAgB;AAGpB,WAAS,QAAQ,GAAG,QAAQ,iBAAiB,QAAQ,SAAS;AAC5D,UAAM,aAAa,WAAW,KAAK;AACnC,WAAO,cAAc,MAAM,kCAAkC,KAAK,EAAE;AACpE,UAAM,WAAW,YAAY;AAAA,MAC3B;AAAA,MACA,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,QAAI,cAAc,UAAU;AAC1B,uBAAiB,aAAa;AAC9B,iBAAW,KAAK,IAAI;AAAA,IACtB;AAAA,EACF;AAIA,MAAI,CAAC,kBAAkB,eAAe,CAAC,GAAG;AACxC,aAAS,QAAQ,GAAG,QAAQ,iBAAiB,QAAQ,SAAS;AAC5D,YAAM,WAAW,WAAW,KAAK;AACjC,aAAO,YAAY,MAAM,kCAAkC,KAAK,EAAE;AAClE,YAAM,aAAa,WAAW;AAC9B,YAAM,WAAW,YAAY;AAAA,QAC3B;AAAA,QACA,YAAY;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AACD,UAAI,aAAa,UAAU;AACzB,yBAAiB,WAAW;AAC5B,mBAAW,KAAK,IAAI;AAGpB,YAAI,kBAAkB,eAAe,CAAC,GAAG;AACvC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,kCAAkC;AACxC,IAAM,iBAAiB;AAAA,EACrB,SAAS,UAAQ;AACf,6BAAyB,cAAc;AACvC,WAAO,eAAe,QAAQ,IAAI;AAAA,EACpC;AAAA,EACA,SAAS,CAAC,MAAM,UAAU;AACxB,6BAAyB,cAAc;AACvC,mBAAe,QAAQ,MAAM,KAAK;AAAA,EACpC;AACF;AACA,IAAM,cAAc,CAAC;AACrB,SAAS,2BAA2B;AAAA,EAClC,aAAa;AAAA,EACb;AAAA,EACA,WAAW,qBAAqB;AAAA,EAChC;AAAA,EACA;AAAA,EACA,IAAI,cAAc;AAAA,EAClB,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS,OAAO;AAAA,EAChB,GAAG;AACL,GAAG;AACD,QAAM,UAAU,YAAY,WAAW;AACvC,QAAM,2BAAuB,qBAAO,IAAI;AACxC,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,IAAI;AAC/C,QAAM,CAAC,QAAQ,SAAS,QAAI,uBAAS,CAAC,CAAC;AACvC,QAAM,cAAc,eAAe;AACnC,QAAM,sCAAkC,qBAAO,CAAC,CAAC;AACjD,QAAM,iCAA6B,qBAAO,oBAAI,IAAI,CAAC;AACnD,QAAM,mBAAe,qBAAO,CAAC;AAC7B,QAAM,yBAAqB,qBAAO;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,qBAAiB,qBAAO;AAAA,IAC5B;AAAA,IACA,gBAAgB,CAAC;AAAA,IACjB,uBAAuB;AAAA,EACzB,CAAC;AACD,QAAM,qBAAiB,qBAAO;AAAA,IAC5B,yBAAyB;AAAA,IACzB,+BAA+B;AAAA,IAC/B,cAAc,CAAC;AAAA,EACjB,CAAC;AACD,wCAAoB,cAAc,OAAO;AAAA,IACvC,OAAO,MAAM,mBAAmB,QAAQ;AAAA,IACxC,WAAW,MAAM;AACf,YAAM;AAAA,QACJ,QAAAC;AAAA,MACF,IAAI,eAAe;AACnB,aAAOA;AAAA,IACT;AAAA,IACA,WAAW,kBAAgB;AACzB,YAAM;AAAA,QACJ,UAAAC;AAAA,MACF,IAAI,mBAAmB;AACvB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,MACF,IAAI,eAAe;AACnB,YAAM,aAAa,yBAAyB;AAAA,QAC1C,QAAQ;AAAA,QACR,kBAAkB,eAAe,IAAI,eAAa,UAAU,WAAW;AAAA,MACzE,CAAC;AACD,UAAI,CAAC,SAAS,YAAY,UAAU,GAAG;AACrC,kBAAU,UAAU;AACpB,uBAAe,QAAQ,SAAS;AAChC,YAAIA,WAAU;AACZ,UAAAA,UAAS,UAAU;AAAA,QACrB;AACA,2BAAmB,gBAAgB,YAAY,gCAAgC,OAAO;AAAA,MACxF;AAAA,IACF;AAAA,EACF,IAAI,CAAC,CAAC;AACN,4BAA0B,MAAM;AAC9B,uBAAmB,QAAQ,aAAa;AACxC,uBAAmB,QAAQ,YAAY;AACvC,uBAAmB,QAAQ,YAAY;AACvC,uBAAmB,QAAQ,KAAK;AAChC,uBAAmB,QAAQ,WAAW;AACtC,uBAAmB,QAAQ,UAAU;AAAA,EACvC,CAAC;AACD,sCAAoC;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB,eAAe,QAAQ;AAAA,IACvC;AAAA,IACA,mBAAmB,qBAAqB;AAAA,EAC1C,CAAC;AACD,8BAAU,MAAM;AACd,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AAGnB,QAAI,YAAY;AACd,UAAI,OAAO,WAAW,KAAK,OAAO,WAAW,eAAe,QAAQ;AAClE;AAAA,MACF;AACA,UAAI,gBAAgB,YAAY,UAAU;AAG1C,UAAI,iBAAiB,MAAM;AACzB,wBAAgB,SAAS,qBAAqB,+BAA+B;AAC7E,oBAAY,UAAU,IAAI;AAAA,MAC5B;AAIA,YAAM,uBAAuB,CAAC,GAAG,cAAc;AAC/C,YAAM,iCAAiC,IAAI,IAAI,2BAA2B,OAAO;AACjF,oBAAc,YAAY,sBAAsB,gCAAgC,QAAQ,OAAO;AAAA,IACjG;AAAA,EACF,GAAG,CAAC,YAAY,QAAQ,OAAO,CAAC;AAGhC,8BAAU,MAAM;AACd;AACE,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,eAAe;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,eAAe;AACnB,UAAI,CAAC,yBAAyB;AAC5B,cAAM,WAAW,eAAe,IAAI,CAAC;AAAA,UACnC;AAAA,QACF,MAAM,EAAE;AACR,uBAAe,QAAQ,eAAe;AACtC,cAAM,oBAAoB,aAAa,SAAS,KAAK,CAAC,SAAS,cAAc,QAAQ;AACrF,YAAI,mBAAmB;AACrB,cAAI,eAAe,KAAK,CAAC;AAAA,YACvB;AAAA,YACA;AAAA,UACF,MAAM,CAAC,iBAAiB,SAAS,IAAI,GAAG;AACtC,2BAAe,QAAQ,0BAA0B;AACjD,oBAAQ,KAAK,oFAAoF;AAAA,UACnG;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,+BAA+B;AAClC,cAAM,mBAAmB,eAAe,IAAI,eAAa,UAAU,WAAW;AAC9E,iBAAS,aAAa,GAAG,aAAa,iBAAiB,QAAQ,cAAc;AAC3E,gBAAM,YAAY,eAAe,UAAU;AAC3C,iBAAO,WAAW,kCAAkC,UAAU,EAAE;AAChE,gBAAM,UAAU,yBAAyB;AAAA,YACvC;AAAA,YACA,SAAS,UAAU;AAAA,YACnB;AAAA,UACF,CAAC;AACD,cAAI,CAAC,SAAS;AACZ,2BAAe,QAAQ,gCAAgC;AACvD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,QAAM,oBAAgB,0BAAY,eAAa;AAC7C,UAAM;AAAA,MACJ,UAAAA;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,QAAI,UAAU,YAAY,aAAa;AACrC,YAAM,wBAAwB,eAAe,IAAI,CAAAF,eAAaA,WAAU,WAAW;AACnF,YAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,MACF,IAAI,gBAAgB,gBAAgB,WAAW,UAAU;AACzD,aAAO,aAAa,MAAM,mCAAmC,UAAU,EAAE,GAAG;AAC5E,UAAI,CAAC,oBAAoB,WAAW,aAAa,GAAG;AAGlD,mCAA2B,QAAQ,IAAI,UAAU,IAAI,SAAS;AAC9D,cAAM,cAAc,mBAAmB,gBAAgB,SAAS,MAAM,eAAe,SAAS;AAC9F,cAAM,QAAQ,cAAc,YAAY,gBAAgB,gBAAgB;AACxE,cAAM,aAAa,oBAAoB;AAAA,UACrC;AAAA,UACA,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,YAAI,CAAC,eAAe,YAAY,UAAU,GAAG;AAC3C,oBAAU,UAAU;AACpB,yBAAe,QAAQ,SAAS;AAChC,cAAIE,WAAU;AACZ,YAAAA,UAAS,UAAU;AAAA,UACrB;AACA,6BAAmB,gBAAgB,YAAY,gCAAgC,OAAO;AAAA,QACxF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,QAAM,kBAAc,0BAAY,CAAC,WAAW,oBAAoB;AAC9D,UAAM;AAAA,MACJ,UAAAA;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,QAAI,UAAU,YAAY,aAAa;AACrC,YAAM,wBAAwB,eAAe,IAAI,CAAAF,eAAaA,WAAU,WAAW;AACnF,YAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,SAAS,mBAAmB;AAAA,QAC5B;AAAA,MACF,IAAI,gBAAgB,gBAAgB,WAAW,UAAU;AACzD,YAAM,UAAU,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB;AAC3F,UAAI,oBAAoB,WAAW,aAAa,GAAG;AAEjD,cAAM,gBAAgB,2BAA2B,QAAQ,IAAI,UAAU,EAAE;AACzE,cAAM,WAAW,iBAAiB,QAAQ,iBAAiB,UAAU,gBAAgB;AACrF,cAAM,cAAc,mBAAmB,gBAAgB,SAAS,MAAM,eAAe,SAAS;AAC9F,cAAM,QAAQ,cAAc,YAAY,WAAW,WAAW;AAC9D,cAAM,aAAa,oBAAoB;AAAA,UACrC;AAAA,UACA,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,YAAI,CAAC,eAAe,YAAY,UAAU,GAAG;AAC3C,oBAAU,UAAU;AACpB,yBAAe,QAAQ,SAAS;AAChC,cAAIE,WAAU;AACZ,YAAAA,UAAS,UAAU;AAAA,UACrB;AACA,6BAAmB,gBAAgB,YAAY,gCAAgC,OAAO;AAAA,QACxF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,QAAM,mBAAe,0BAAY,eAAa;AAC5C,UAAM;AAAA,MACJ,QAAAD;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,gBAAgB,gBAAgB,WAAWA,OAAM;AACrD,WAAO,aAAa,MAAM,mCAAmC,UAAU,EAAE,GAAG;AAC5E,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAGL,QAAM,oBAAgB,0BAAY,CAAC,WAAW,gBAAgB;AAC5D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,aAAa,mBAAmB,gBAAgB,SAAS;AAC/D,WAAO,yBAAyB;AAAA,MAC9B;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,WAAW,MAAM,CAAC;AAGtB,QAAM,uBAAmB,0BAAY,eAAa;AAChD,UAAM;AAAA,MACJ,QAAAA;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM;AAAA,MACJ,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,gBAAgB,WAAWA,OAAM;AACrD,WAAO,aAAa,MAAM,mCAAmC,UAAU,EAAE,GAAG;AAC5E,WAAO,gBAAgB,QAAQ,oBAAoB,WAAW,aAAa;AAAA,EAC7E,GAAG,CAAC,CAAC;AAGL,QAAM,sBAAkB,0BAAY,eAAa;AAC/C,UAAM;AAAA,MACJ,QAAAA;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM;AAAA,MACJ,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,gBAAgB,WAAWA,OAAM;AACrD,WAAO,aAAa,MAAM,mCAAmC,UAAU,EAAE,GAAG;AAC5E,WAAO,CAAC,eAAe,oBAAoB,WAAW,aAAa,IAAI;AAAA,EACzE,GAAG,CAAC,CAAC;AACL,QAAM,oBAAgB,0BAAY,eAAa;AAC7C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AACnB,mBAAe,KAAK,SAAS;AAC7B,mBAAe,KAAK,CAAC,QAAQ,WAAW;AACtC,YAAM,SAAS,OAAO;AACtB,YAAM,SAAS,OAAO;AACtB,UAAI,UAAU,QAAQ,UAAU,MAAM;AACpC,eAAO;AAAA,MACT,WAAW,UAAU,MAAM;AACzB,eAAO;AAAA,MACT,WAAW,UAAU,MAAM;AACzB,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS;AAAA,MAClB;AAAA,IACF,CAAC;AACD,mBAAe,QAAQ,wBAAwB;AAC/C,gBAAY;AAAA,EACd,GAAG,CAAC,WAAW,CAAC;AAIhB,4BAA0B,MAAM;AAC9B,QAAI,eAAe,QAAQ,uBAAuB;AAChD,qBAAe,QAAQ,wBAAwB;AAC/C,YAAM;AAAA,QACJ,YAAAE;AAAA,QACA,UAAAD;AAAA,QACA,SAAAE;AAAA,MACF,IAAI,mBAAmB;AACvB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,MACF,IAAI,eAAe;AAInB,UAAI,eAAe;AACnB,UAAID,aAAY;AACd,cAAM,QAAQ,oBAAoBA,aAAY,gBAAgBC,QAAO;AACrE,YAAI,OAAO;AACT,qCAA2B,UAAU,IAAI,IAAI,OAAO,QAAQ,MAAM,aAAa,CAAC;AAChF,yBAAe,MAAM;AAAA,QACvB;AAAA,MACF;AACA,UAAI,gBAAgB,MAAM;AACxB,uBAAe,6BAA6B;AAAA,UAC1C;AAAA,QACF,CAAC;AAAA,MACH;AAIA,YAAM,aAAa,yBAAyB;AAAA,QAC1C,QAAQ;AAAA,QACR,kBAAkB,eAAe,IAAI,eAAa,UAAU,WAAW;AAAA,MACzE,CAAC;AACD,UAAI,CAAC,SAAS,YAAY,UAAU,GAAG;AACrC,kBAAU,UAAU;AACpB,uBAAe,QAAQ,SAAS;AAChC,YAAIF,WAAU;AACZ,UAAAA,UAAS,UAAU;AAAA,QACrB;AACA,2BAAmB,gBAAgB,YAAY,gCAAgC,OAAO;AAAA,MACxF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,4BAA0B,MAAM;AAC9B,UAAM,cAAc,eAAe;AACnC,WAAO,MAAM;AACX,kBAAY,SAAS,CAAC;AAAA,IACxB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAMG,4BAAuB,0BAAY,kBAAgB;AACvD,QAAI,QAAQ;AACZ,UAAM,oBAAoB,qBAAqB;AAC/C,QAAI,mBAAmB;AACrB,YAAMC,SAAQ,OAAO,iBAAiB,mBAAmB,IAAI;AAC7D,UAAIA,OAAM,iBAAiB,WAAW,MAAM,OAAO;AACjD,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,WAAO,SAAS,cAAc,OAAO;AACnC,YAAM,eAAe;AACrB,YAAMC,qBAAoB,qBAAqB;AAC/C,UAAI,CAACA,oBAAmB;AACtB,eAAO,MAAM;AAAA,MACf;AACA,YAAM;AAAA,QACJ,WAAAC;AAAA,QACA,WAAAC;AAAA,QACA,IAAIC;AAAA,QACJ,kBAAAC;AAAA,QACA,UAAAT;AAAA,MACF,IAAI,mBAAmB;AACvB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,MACF,IAAI,eAAe;AACnB,YAAM;AAAA,QACJ;AAAA,MACF,IAAIO,eAAc,QAAQA,eAAc,SAASA,aAAY,CAAC;AAC9D,YAAM,eAAe,sBAAsBC,UAAS,cAAcH,kBAAiB;AACnF,UAAI,QAAQ,yBAAyB,OAAO,cAAcC,YAAWC,YAAWE,mBAAkBJ,kBAAiB;AACnH,YAAM,eAAeC,eAAc;AACnC,UAAI,gBAAgB,OAAO;AACzB,gBAAQ,CAAC;AAAA,MACX;AACA,YAAM,mBAAmB,eAAe,IAAI,eAAa,UAAU,WAAW;AAC9E,YAAM,aAAa,oBAAoB;AAAA,QACrC;AAAA,QACA,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,gBAAgB;AAAA,QACpF;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,UAAU,KAAK,IAAI,aAAa;AAAA,MAC3C,CAAC;AACD,YAAM,gBAAgB,CAAC,eAAe,YAAY,UAAU;AAI5D,UAAI,eAAe,KAAK,KAAK,aAAa,KAAK,GAAG;AAIhD,YAAI,aAAa,WAAW,OAAO;AACjC,uBAAa,UAAU;AACvB,cAAI,CAAC,iBAAiB,UAAU,GAAG;AAGjC,gBAAI,cAAc;AAChB,yCAA2B,cAAc,QAAQ,IAAI,0BAA0B,uBAAuB;AAAA,YACxG,OAAO;AACL,yCAA2B,cAAc,QAAQ,IAAI,wBAAwB,qBAAqB;AAAA,YACpG;AAAA,UACF,OAAO;AACL,uCAA2B,cAAc,CAAC;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AACA,UAAI,eAAe;AACjB,kBAAU,UAAU;AACpB,uBAAe,QAAQ,SAAS;AAChC,YAAIN,WAAU;AACZ,UAAAA,UAAS,UAAU;AAAA,QACrB;AACA,2BAAmB,gBAAgB,YAAY,gCAAgC,OAAO;AAAA,MACxF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,QAAMb,mBAAc,0BAAY,CAAC,WAAW,oBAAoB;AAC9D,UAAM;AAAA,MACJ,UAAAa;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,wBAAwB,eAAe,IAAI,CAAAF,eAAaA,WAAU,WAAW;AACnF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,gBAAgB,WAAW,UAAU;AACzD,WAAO,aAAa,MAAM,mCAAmC,UAAU,EAAE,GAAG;AAC5E,UAAM,cAAc,mBAAmB,gBAAgB,SAAS,MAAM,eAAe,SAAS;AAC9F,UAAM,QAAQ,cAAc,YAAY,kBAAkB,kBAAkB;AAC5E,UAAM,aAAa,oBAAoB;AAAA,MACrC;AAAA,MACA,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AACD,QAAI,CAAC,eAAe,YAAY,UAAU,GAAG;AAC3C,gBAAU,UAAU;AACpB,qBAAe,QAAQ,SAAS;AAChC,UAAIE,WAAU;AACZ,QAAAA,UAAS,UAAU;AAAA,MACrB;AACA,yBAAmB,gBAAgB,YAAY,gCAAgC,OAAO;AAAA,IACxF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,iCAA6B,0BAAY,CAAC,WAAW,oBAAoB;AAC7E,UAAM;AAAA,MACJ,QAAAD;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM;AAAA,MACJ,eAAe,oBAAoB;AAAA,MACnC,aAAa;AAAA,IACf,IAAI;AACJ,UAAM;AAAA,MACJ,eAAe,oBAAoB;AAAA,MACnC,aAAa;AAAA,MACb,SAAS,cAAc;AAAA,MACvB,SAAS,cAAc;AAAA,IACzB,IAAI,UAAU;AACd,UAAM;AAAA,MACJ,WAAW;AAAA,IACb,IAAI,gBAAgB,gBAAgB,WAAWA,OAAM;AACrD,QAAI,iBAAiB,MAAM;AAEzB;AAAA,IACF;AACA,QAAI,mBAAmB,mBAAmB,oBAAoB,eAAe,iBAAiB,GAAG;AAC/F,UAAI,CAAC,oBAAoB,mBAAmB,iBAAiB,GAAG;AAC9D,QAAAZ,aAAY,WAAW,iBAAiB;AAAA,MAC1C;AAAA,IACF,WAAW,gBAAgB,aAAa;AACtC,MAAAA,aAAY,WAAW,WAAW;AAAA,IACpC,WAAW,gBAAgB,aAAa;AACtC,MAAAA,aAAY,WAAW,WAAW;AAAA,IACpC;AAAA,EACF,GAAG,CAACA,YAAW,CAAC;AAGhB,QAAM,oBAAgB,0BAAY,CAAC,cAAc,UAAU;AACzD,UAAM;AAAA,MACJ,WAAAmB;AAAA,IACF,IAAI,mBAAmB;AACvB,UAAM;AAAA,MACJ,QAAAP;AAAA,IACF,IAAI,eAAe;AACnB,QAAI,CAAC,qBAAqB,SAAS;AACjC;AAAA,IACF;AACA,UAAM,gBAAgB,uBAAuB,cAAc,qBAAqB,OAAO;AACvF,WAAO,eAAe,yCAAyC,YAAY,GAAG;AAC9E,UAAM,wBAAwB,6BAA6BO,YAAW,KAAK;AAC3E,iBAAa;AAAA,MACX;AAAA,MACA,gBAAgB,cAAc,sBAAsB;AAAA,MACpD;AAAA,MACA,eAAeP;AAAA,IACjB,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,QAAM,mBAAe,0BAAY,MAAM;AACrC,iBAAa,IAAI;AAAA,EACnB,GAAG,CAAC,CAAC;AACL,QAAM,sBAAkB,0BAAY,eAAa;AAC/C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,QAAQ,mBAAmB,gBAAgB,SAAS;AAC1D,QAAI,SAAS,GAAG;AACd,qBAAe,OAAO,OAAO,CAAC;AAM9B,aAAO,gCAAgC,QAAQ,UAAU,EAAE;AAC3D,qBAAe,QAAQ,wBAAwB;AAC/C,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,QAAM,cAAU,sBAAQ,OAAO;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,sBAAAI;AAAA,IACA,aAAAhB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmB,qBAAqB;AAAA,EAC1C,IAAI,CAAC,eAAe,WAAW,WAAW,aAAa,cAAc,eAAe,SAAS,kBAAkB,iBAAiB,4BAA4B,eAAegB,uBAAsBhB,cAAa,eAAe,cAAc,eAAe,CAAC;AAC3P,QAAM,QAAQ;AAAA,IACZ,SAAS;AAAA,IACT,eAAe,cAAc,eAAe,QAAQ;AAAA,IACpD,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AACA,aAAO,4BAAc,kBAAkB,UAAU;AAAA,IAC/C,OAAO;AAAA,EACT,OAAG,4BAAc,MAAM;AAAA,IACrB,GAAG;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,OAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA;AAAA,IAEA,CAAC,gBAAgB,KAAK,GAAG;AAAA,IACzB,CAAC,gBAAgB,cAAc,GAAG;AAAA,IAClC,CAAC,gBAAgB,OAAO,GAAG;AAAA,EAC7B,CAAC,CAAC;AACJ;AACA,IAAM,iBAAa,yBAAW,CAACE,QAAO,YAAQ,4BAAc,4BAA4B;AAAA,EACtF,GAAGA;AAAA,EACH,cAAc;AAChB,CAAC,CAAC;AACF,2BAA2B,cAAc;AACzC,WAAW,cAAc;AACzB,SAAS,mBAAmB,gBAAgB,WAAW;AACrD,SAAO,eAAe,UAAU,mBAAiB,kBAAkB,aAAa,cAAc,OAAO,UAAU,EAAE;AACnH;AACA,SAAS,gBAAgB,gBAAgB,WAAW,QAAQ;AAC1D,QAAM,aAAa,mBAAmB,gBAAgB,SAAS;AAC/D,QAAM,cAAc,eAAe,eAAe,SAAS;AAC3D,QAAM,eAAe,cAAc,CAAC,aAAa,GAAG,UAAU,IAAI,CAAC,YAAY,aAAa,CAAC;AAC7F,QAAM,YAAY,OAAO,UAAU;AACnC,SAAO;AAAA,IACL,GAAG,UAAU;AAAA,IACb;AAAA,IACA;AAAA,EACF;AACF;AAIA,SAAS,uCAAuC;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,8BAAU,MAAM;AACd,QAAI,YAAY,iBAAiB,QAAQ,qBAAqB,MAAM;AAClE;AAAA,IACF;AACA,UAAM,gBAAgB,uBAAuB,UAAU,iBAAiB;AACxE,QAAI,iBAAiB,MAAM;AACzB;AAAA,IACF;AACA,UAAM,YAAY,WAAS;AACzB,UAAI,MAAM,kBAAkB;AAC1B;AAAA,MACF;AACA,cAAQ,MAAM,KAAK;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QACH;AACE,gBAAM,eAAe;AACrB,wBAAc,KAAK;AACnB;AAAA,QACF;AAAA,QACF,KAAK,MACH;AACE,gBAAM,eAAe;AACrB,gBAAM,UAAU,cAAc,aAAa,gBAAgB,OAAO;AAClE,iBAAO,SAAS,kCAAkC,OAAO,GAAG;AAC5D,gBAAM,UAAU,gCAAgC,SAAS,iBAAiB;AAC1E,gBAAM,QAAQ,4BAA4B,SAAS,UAAU,iBAAiB;AAC9E,iBAAO,UAAU,MAAM,mCAAmC,QAAQ,GAAG;AACrE,gBAAM,YAAY,MAAM,WAAW,QAAQ,IAAI,QAAQ,IAAI,QAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,SAAS,QAAQ,IAAI;AACzH,gBAAM,aAAa,QAAQ,SAAS;AACpC,qBAAW,MAAM;AACjB;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AACA,kBAAc,iBAAiB,WAAW,SAAS;AACnD,WAAO,MAAM;AACX,oBAAc,oBAAoB,WAAW,SAAS;AAAA,IACxD;AAAA,EACF,GAAG,CAAC,mBAAmB,UAAU,UAAU,aAAa,CAAC;AAC3D;AAEA,SAAS,kBAAkB;AAAA,EACzB,WAAW;AAAA,EACX,WAAW,qBAAqB;AAAA,EAChC,WAAW;AAAA,EACX;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,iBAAiB,CAAC;AAAA,EACzB,WAAW;AAAA,EACX,SAAS,OAAO;AAAA,EAChB,GAAG;AACL,GAAG;AACD,MAAI,uBAAuB;AAC3B,QAAM,iBAAa,qBAAO,IAAI;AAG9B,QAAM,mBAAe,qBAAO;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,8BAAU,MAAM;AACd,iBAAa,QAAQ,UAAU;AAC/B,iBAAa,QAAQ,aAAa;AAClC,iBAAa,QAAQ,gBAAgB;AACrC,iBAAa,QAAQ,cAAc;AAAA,EACrC,CAAC;AACD,QAAM,wBAAoB,yBAAW,iBAAiB;AACtD,MAAI,sBAAsB,MAAM;AAC9B,UAAM,MAAM,6EAA6E;AAAA,EAC3F;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,sBAAsB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,iBAAiB,YAAY,WAAW;AAC9C,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,UAAU;AAC7C,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,KAAK;AAChD,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,IAAI;AACvD,QAAM,yBAAqB,qBAAO;AAAA,IAChC;AAAA,EACF,CAAC;AACD,4BAA0B,MAAM;AAC9B,uBAAmB,QAAQ,QAAQ;AAAA,EACrC,CAAC;AACD,8BAAU,MAAM;AACd,QAAI,UAAU;AACZ,uBAAiB,IAAI;AAAA,IACvB,OAAO;AACL,YAAMqB,iBAAgB,oCAAoC,cAAc;AACxE,uBAAiB,MAAMA,cAAa;AAAA,IACtC;AAAA,EACF,GAAG,CAAC,UAAU,gBAAgB,mCAAmC,CAAC;AAIlE,QAAM,wBAAwB,wBAAwB,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,YAAY,QAAQ,0BAA0B,SAAS,wBAAwB;AACpN,QAAM,sBAAsB,uBAAuB,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,UAAU,QAAQ,yBAAyB,SAAS,uBAAuB;AAC7M,8BAAU,MAAM;AACd,QAAI,YAAY,iBAAiB,MAAM;AACrC;AAAA,IACF;AACA,UAAM,UAAU,WAAW;AAC3B,WAAO,SAAS,0BAA0B;AAC1C,QAAI,UAAU;AACd,UAAM,wBAAwB,CAAC,QAAQ,UAAU,UAAU;AACzD,UAAI,CAAC,UAAU;AACb,iBAAS,UAAU;AACnB;AAAA,MACF;AACA,cAAQ,QAAQ;AAAA,QACd,KAAK,QACH;AACE,mBAAS,MAAM;AACf,oBAAU;AACV,iBAAO,OAAO,gDAAgD;AAC9D,wBAAc,gBAAgB,KAAK;AACnC,gBAAM;AAAA,YACJ,YAAAC;AAAA,YACA,eAAAC;AAAA,UACF,IAAI,aAAa;AACjB,UAAAD,gBAAe,QAAQA,gBAAe,SAAS,SAASA,YAAW,IAAI;AACvE,UAAAC,mBAAkB,QAAQA,mBAAkB,SAAS,SAASA,eAAc;AAC5E;AAAA,QACF;AAAA,QACF,KAAK,QACH;AACE,gBAAM;AAAA,YACJ,OAAAC;AAAA,UACF,IAAI,mBAAmB;AACvB,oBAAU;AACV,cAAIA,WAAU,QAAQ;AACpB,qBAAS,OAAO;AAAA,UAClB;AACA,iBAAO,OAAO,gDAAgD;AAC9D,wBAAc,KAAK;AACnB;AAAA,QACF;AAAA,QACF,KAAK,MACH;AACE,mBAAS,OAAO;AAChB,uBAAa;AACb,gBAAM;AAAA,YACJ,SAAAC;AAAA,YACA,YAAAH;AAAA,YACA,aAAAI;AAAA,UACF,IAAI,aAAa;AACjB,UAAAJ,gBAAe,QAAQA,gBAAe,SAAS,SAASA,YAAW,KAAK;AACxE,UAAAI,iBAAgB,QAAQA,iBAAgB,SAAS,SAASA,aAAY;AACtE,cAAI,CAAC,SAAS;AACZ,YAAAD,aAAY,QAAQA,aAAY,SAAS,SAASA,SAAQ;AAAA,UAC5D;AACA;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AACA,WAAO,qBAAqB,gBAAgB,SAAS,WAAW;AAAA,MAC9D,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,GAAG,qBAAqB;AAAA,EAC1B,GAAG,CAAC,sBAAsB,WAAW,UAAU,oBAAoB,qCAAqC,gBAAgB,eAAe,eAAe,YAAY,CAAC;AACnK,yCAAuC;AAAA,IACrC;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AACA,aAAO,4BAAc,MAAM;AAAA,IACzB,GAAG;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,QAAQ,MAAM;AACZ,mBAAa,KAAK;AAClB,iBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,IACzD;AAAA,IACA,SAAS,MAAM;AACb,mBAAa,IAAI;AACjB,kBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,IAC5D;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA;AAAA;AAAA,IAEA,CAAC,gBAAgB,cAAc,GAAG;AAAA,IAClC,CAAC,gBAAgB,OAAO,GAAG;AAAA,IAC3B,CAAC,gBAAgB,YAAY,GAAG;AAAA,IAChC,CAAC,gBAAgB,kBAAkB,GAAG,UAAU,SAAS,YAAY,YAAY,aAAa;AAAA,IAC9F,CAAC,gBAAgB,mBAAmB,GAAG,CAAC;AAAA,IACxC,CAAC,gBAAgB,cAAc,GAAG;AAAA,IAClC,CAAC,gBAAgB,iBAAiB,GAAG;AAAA,EACvC,CAAC;AACH;AACA,kBAAkB,cAAc;AAEhC,SAAS,uBAAuB;AAC9B,QAAM,cAAU,yBAAW,iBAAiB;AAC5C,SAAO;AAAA,IACL,WAAW,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,IACrE,SAAS,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,EACrE;AACF;AAEA,SAAS,gBAAgB,IAAI,QAAQ,UAAU;AAC7C,QAAM,UAAU,MAAM,cAAc,mBAAmB,EAAE,IAAI;AAC7D,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,yBAAyB,SAAS,QAAQ,UAAU;AAC3D,SAAO,MAAM,KAAK,MAAM,iBAAiB,qCAAqC,OAAO,IAAI,CAAC;AAC5F;AAEA,SAAS,yBAAyB,SAAS,SAAS,QAAQ;AAC1D,MAAI,CAAC,WAAW,SAAS,SAAS,MAAM,GAAG;AACzC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC;AAAA,IAChC,GAAG,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC;AAAA,IAChC,OAAO,KAAK,IAAI,QAAQ,IAAI,QAAQ,OAAO,QAAQ,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC;AAAA,IACrG,QAAQ,KAAK,IAAI,QAAQ,IAAI,QAAQ,QAAQ,QAAQ,IAAI,QAAQ,MAAM,IAAI,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC;AAAA,EAC1G;AACF;", "names": ["resizePanel", "minSize", "props", "nonce", "count", "delta", "pivotIndex", "prevSize", "unsafeSize", "safeSize", "panelDataArray", "panelData", "layout", "onLayout", "autoSaveId", "storage", "registerResizeHandle", "style", "panelGroupElement", "direction", "dragState", "groupId", "keyboardResizeBy", "resize<PERSON><PERSON>ler", "onDragging", "onPointerDown", "state", "onClick", "onPointerUp"]}