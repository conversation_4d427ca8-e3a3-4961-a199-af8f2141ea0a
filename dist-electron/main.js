import { app, BrowserWindow, ipc<PERSON>ain, <PERSON>u, shell } from "electron";
import { createRequire } from "node:module";
import { fileURLToPath } from "node:url";
import path from "node:path";
createRequire(import.meta.url);
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.DIST_ELECTRON = path.join(__dirname, "./");
process.env.DIST = path.join(__dirname, "../dist");
process.env.VITE_PUBLIC = process.env.VITE_DEV_SERVER_URL ? path.join(__dirname, "../public") : process.env.DIST;
if (process.platform === "win32") app.disableHardwareAcceleration();
if (process.platform === "win32") app.setAppUserModelId(app.getName());
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}
if (process.env.NODE_ENV === "development") {
  app.whenReady().then(() => {
  });
}
let win = null;
const preload = path.join(__dirname, "./preload.js");
const url = "http://localhost:5173";
const indexHtml = path.join(process.env.DIST, "index.html");
async function createWindow() {
  win = new BrowserWindow({
    title: "실시간 번역 앱",
    icon: path.join(process.env.VITE_PUBLIC, "favicon.ico"),
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false
    }
  });
  {
    win.loadURL(url);
    win.webContents.openDevTools();
  }
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  win.webContents.setWindowOpenHandler(({ url: url2 }) => {
    if (url2.startsWith("https:")) shell.openExternal(url2);
    return { action: "deny" };
  });
}
app.whenReady().then(createWindow);
app.on("window-all-closed", () => {
  win = null;
  if (process.platform !== "darwin") app.quit();
});
app.on("second-instance", () => {
  if (win) {
    if (win.isMinimized()) win.restore();
    win.focus();
  }
});
app.on("activate", () => {
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createWindow();
  }
});
ipcMain.handle("open-win", (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`);
  } else {
    childWindow.loadFile(indexHtml, { hash: arg });
  }
});
const template = [
  {
    label: "파일",
    submenu: [
      {
        label: "종료",
        accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
        click: () => {
          app.quit();
        }
      }
    ]
  },
  {
    label: "편집",
    submenu: [
      { label: "실행 취소", accelerator: "CmdOrCtrl+Z", role: "undo" },
      { label: "다시 실행", accelerator: "Shift+CmdOrCtrl+Z", role: "redo" },
      { type: "separator" },
      { label: "잘라내기", accelerator: "CmdOrCtrl+X", role: "cut" },
      { label: "복사", accelerator: "CmdOrCtrl+C", role: "copy" },
      { label: "붙여넣기", accelerator: "CmdOrCtrl+V", role: "paste" }
    ]
  },
  {
    label: "보기",
    submenu: [
      { label: "새로고침", accelerator: "CmdOrCtrl+R", role: "reload" },
      { label: "강제 새로고침", accelerator: "CmdOrCtrl+Shift+R", role: "forceReload" },
      { label: "개발자 도구", accelerator: "F12", role: "toggleDevTools" },
      { type: "separator" },
      { label: "실제 크기", accelerator: "CmdOrCtrl+0", role: "resetZoom" },
      { label: "확대", accelerator: "CmdOrCtrl+Plus", role: "zoomIn" },
      { label: "축소", accelerator: "CmdOrCtrl+-", role: "zoomOut" },
      { type: "separator" },
      { label: "전체 화면", accelerator: "F11", role: "togglefullscreen" }
    ]
  },
  {
    label: "창",
    submenu: [
      { label: "최소화", accelerator: "CmdOrCtrl+M", role: "minimize" },
      { label: "닫기", accelerator: "CmdOrCtrl+W", role: "close" }
    ]
  }
];
if (process.platform === "darwin") {
  template.unshift({
    label: app.getName(),
    submenu: [
      { label: "앱 정보", role: "about" },
      { type: "separator" },
      { label: "서비스", role: "services" },
      { type: "separator" },
      { label: "숨기기", accelerator: "Command+H", role: "hide" },
      { label: "다른 항목 숨기기", accelerator: "Command+Shift+H", role: "hideOthers" },
      { label: "모두 보기", role: "unhide" },
      { type: "separator" },
      { label: "종료", accelerator: "Command+Q", click: () => app.quit() }
    ]
  });
}
const menu = Menu.buildFromTemplate(template);
Menu.setApplicationMenu(menu);
