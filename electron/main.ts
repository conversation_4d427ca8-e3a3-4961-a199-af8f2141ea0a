import { app, <PERSON><PERSON><PERSON>Window, <PERSON>u, shell, ipc<PERSON>ain } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬ dist-electron
// │ ├─┬ main.js    > Electron-Main
// │ └─┬ preload.js > Preload-Scripts
// ├─┬ dist
// │ └── index.html  > Electron-Renderer
//
process.env.DIST_ELECTRON = path.join(__dirname, './')
process.env.DIST = path.join(__dirname, '../dist')
process.env.VITE_PUBLIC = process.env.VITE_DEV_SERVER_URL
  ? path.join(__dirname, '../public')
  : process.env.DIST

// Disable GPU Acceleration for Windows 7
if (process.platform === 'win32') app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

// Install "react devtools"
if (process.env.NODE_ENV === 'development') {
  app.whenReady().then(() => {
    // Install React Developer Tools
    // Note: You might need to install electron-devtools-installer
    // import installExtension, { REACT_DEVELOPER_TOOLS } from 'electron-devtools-installer'
    // installExtension(REACT_DEVELOPER_TOOLS)
  })
}

let win: BrowserWindow | null = null
// Here, you can also use other preload
const preload = path.join(__dirname, './preload.js')
const url = 'http://localhost:5173'
const indexHtml = path.join(process.env.DIST, 'index.html')

async function createWindow() {
  win = new BrowserWindow({
    title: '실시간 번역 앱',
    icon: path.join(process.env.VITE_PUBLIC, 'favicon.ico'),
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false,
    },
  })

  if (url) { // Development mode
    win.loadURL(url)
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    win.loadFile(indexHtml)
  }

  // Test actively push message to the Electron-Renderer
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })

  // Apply electron-updater
  // update(win)
}

app.whenReady().then(createWindow)

app.on('window-all-closed', () => {
  win = null
  if (process.platform !== 'darwin') app.quit()
})

app.on('second-instance', () => {
  if (win) {
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore()
    win.focus()
  }
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow()
  }
})

// New window example arg: new windows url
ipcMain.handle('open-win', (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: false,
    },
  })

  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`)
  } else {
    childWindow.loadFile(indexHtml, { hash: arg })
  }
})

// Create application menu
const template: Electron.MenuItemConstructorOptions[] = [
  {
    label: '파일',
    submenu: [
      {
        label: '종료',
        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
        click: () => {
          app.quit()
        }
      }
    ]
  },
  {
    label: '편집',
    submenu: [
      { label: '실행 취소', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
      { label: '다시 실행', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
      { type: 'separator' },
      { label: '잘라내기', accelerator: 'CmdOrCtrl+X', role: 'cut' },
      { label: '복사', accelerator: 'CmdOrCtrl+C', role: 'copy' },
      { label: '붙여넣기', accelerator: 'CmdOrCtrl+V', role: 'paste' }
    ]
  },
  {
    label: '보기',
    submenu: [
      { label: '새로고침', accelerator: 'CmdOrCtrl+R', role: 'reload' },
      { label: '강제 새로고침', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
      { label: '개발자 도구', accelerator: 'F12', role: 'toggleDevTools' },
      { type: 'separator' },
      { label: '실제 크기', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
      { label: '확대', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
      { label: '축소', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
      { type: 'separator' },
      { label: '전체 화면', accelerator: 'F11', role: 'togglefullscreen' }
    ]
  },
  {
    label: '창',
    submenu: [
      { label: '최소화', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
      { label: '닫기', accelerator: 'CmdOrCtrl+W', role: 'close' }
    ]
  }
]

if (process.platform === 'darwin') {
  template.unshift({
    label: app.getName(),
    submenu: [
      { label: '앱 정보', role: 'about' },
      { type: 'separator' },
      { label: '서비스', role: 'services' },
      { type: 'separator' },
      { label: '숨기기', accelerator: 'Command+H', role: 'hide' },
      { label: '다른 항목 숨기기', accelerator: 'Command+Shift+H', role: 'hideOthers' },
      { label: '모두 보기', role: 'unhide' },
      { type: 'separator' },
      { label: '종료', accelerator: 'Command+Q', click: () => app.quit() }
    ]
  })
}

const menu = Menu.buildFromTemplate(template)
Menu.setApplicationMenu(menu)
