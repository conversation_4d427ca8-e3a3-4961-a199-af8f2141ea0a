import { ScrollArea } from "./ui/scroll-area";
import { Switch } from "./ui/switch";
import { Label } from "./ui/label";
import { TranslationBlock } from "./TranslationBlock";

interface Translation {
  id: string;
  timestamp: string;
  originalText: string;
  translatedText: string;
  originalLang: string;
  targetLang: string;
  confidence: 'high' | 'medium' | 'low';
}

interface TranslationPanelProps {
  translations: Translation[];
  isSummaryPanelVisible: boolean;
  onSummaryPanelToggle: () => void;
  isQuestionHelperVisible: boolean;
  onQuestionHelperToggle: () => void;
}

export function TranslationPanel({ 
  translations,
  isSummaryPanelVisible,
  onSummaryPanelToggle,
  isQuestionHelperVisible,
  onQuestionHelperToggle
}: TranslationPanelProps) {
  const handlePlay = (text: string, lang: string) => {
    console.log(`Playing text: ${text} in language: ${lang}`);
    // 실제로는 TTS API 호출
  };

  return (
    <div className="flex-1 p-4 flex flex-col h-full">
      {/* 헤더 영역 */}
      <div className="mb-4 shrink-0">
        <div className="flex items-start justify-between">
          {/* 좌측: 제목과 설명 */}
          <div>
            <h2 className="text-lg mb-1">실시간 통역</h2>
            <p className="text-sm text-muted-foreground">음성이 실시간으로 문단 단위로 번역되어 표시됩니다</p>
          </div>
          
          {/* 우측: 토글 스위치들 - 그룹핑된 배경과 정렬된 토글들 */}
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200 shadow-sm">
            <div className="flex flex-col gap-2.5">
              {/* 요약 패널 토글 */}
              <div className="flex items-center gap-3">
                <Label className="text-xs text-muted-foreground w-20 text-right">요약 패널:</Label>
                <div className="flex items-center gap-1">
                  <Label className="text-xs text-muted-foreground">숨김</Label>
                  <Switch 
                    checked={isSummaryPanelVisible} 
                    onCheckedChange={onSummaryPanelToggle}
                    className="data-[state=checked]:bg-blue-600 scale-75"
                  />
                  <Label className="text-xs text-muted-foreground">표시</Label>
                </div>
              </div>

              {/* 질문 도우미 토글 */}
              <div className="flex items-center gap-3">
                <Label className="text-xs text-muted-foreground w-20 text-right">질문 도우미:</Label>
                <div className="flex items-center gap-1">
                  <Label className="text-xs text-muted-foreground">숨김</Label>
                  <Switch 
                    checked={isQuestionHelperVisible} 
                    onCheckedChange={onQuestionHelperToggle}
                    className="data-[state=checked]:bg-purple-600 scale-75"
                  />
                  <Label className="text-xs text-muted-foreground">표시</Label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 스크롤 영역 - 남은 공간을 모두 활용 */}
      <ScrollArea className="flex-1 min-h-0">
        <div className="space-y-4 pr-2">
          {translations.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>마이크를 켜고 대화를 시작해주세요</p>
              <p className="text-xs mt-1">문단 단위로 통역 결과가 표시됩니다</p>
            </div>
          ) : (
            translations.map((translation) => (
              <TranslationBlock
                key={translation.id}
                {...translation}
                onPlay={handlePlay}
              />
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  );
}